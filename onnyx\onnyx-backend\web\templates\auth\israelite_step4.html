{% extends "base.html" %}

{% block title %}Gate Keeper Verification - ONNYX Eden Mode{% endblock %}
{% block meta_description %}Your heritage documentation is under review by the Gate Keeper tribal council. Track your verification status and await approval.{% endblock %}

{% block head %}
<style>
    .verification-status-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
    }
    
    .status-card {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        padding: 2rem;
        margin: 2rem 0;
        position: relative;
        overflow: hidden;
    }
    
    .status-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #F59E0B, #EAB308);
    }
    
    .gatekeeper-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4F46E5, #8B5CF6);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        margin: 0 auto 1rem;
        position: relative;
        animation: pulse 3s infinite;
    }
    
    .verification-timeline {
        position: relative;
        padding-left: 2rem;
    }
    
    .timeline-item {
        position: relative;
        padding-bottom: 2rem;
    }
    
    .timeline-item::before {
        content: '';
        position: absolute;
        left: -2rem;
        top: 0.5rem;
        width: 1rem;
        height: 1rem;
        border-radius: 50%;
        background: #4F46E5;
    }
    
    .timeline-item.completed::before {
        background: #10B981;
    }
    
    .timeline-item.current::before {
        background: #F59E0B;
        animation: pulse 2s infinite;
    }
    
    .timeline-item:not(:last-child)::after {
        content: '';
        position: absolute;
        left: -1.75rem;
        top: 1.5rem;
        width: 2px;
        height: calc(100% - 1rem);
        background: rgba(255, 255, 255, 0.2);
    }
    
    .timeline-item.completed:not(:last-child)::after {
        background: #10B981;
    }
    
    .contact-info {
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1));
        border: 1px solid rgba(16, 185, 129, 0.3);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .submitted-files {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        margin: 0.5rem 0;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
    }
    
    .status-indicator {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
    }
    
    .status-pending {
        background: rgba(245, 158, 11, 0.2);
        color: #FCD34D;
        border: 1px solid rgba(245, 158, 11, 0.3);
    }
    
    .status-approved {
        background: rgba(16, 185, 129, 0.2);
        color: #6EE7B7;
        border: 1px solid rgba(16, 185, 129, 0.3);
    }
    
    .progress-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2rem 0;
    }
    
    .progress-step {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 0.5rem;
        font-weight: bold;
        color: white;
        position: relative;
    }
    
    .progress-step.completed {
        background: linear-gradient(135deg, #10B981, #34D399);
    }
    
    .progress-step.current {
        background: linear-gradient(135deg, #F59E0B, #EAB308);
        animation: pulse 2s infinite;
    }
    
    .progress-step.pending {
        background: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.6);
    }
    
    .progress-connector {
        width: 3rem;
        height: 3px;
        background: rgba(255, 255, 255, 0.2);
        margin: 0 0.5rem;
    }
    
    .progress-connector.completed {
        background: linear-gradient(90deg, #10B981, #34D399);
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.05); opacity: 0.8; }
    }
    
    .refresh-btn {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4F46E5, #8B5CF6);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 100;
    }
    
    .refresh-btn:hover {
        transform: scale(1.1);
        background: linear-gradient(135deg, #5B21B6, #7C3AED);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
    <!-- Animated background elements -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
        <div class="absolute top-40 right-20 w-48 h-48 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float-delayed"></div>
        <div class="absolute bottom-20 left-1/3 w-40 h-40 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
    </div>

    <div class="container mx-auto px-4 py-8 relative z-10">
        <!-- Progress Indicator -->
        <div class="progress-indicator">
            <div class="progress-step completed">1</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step completed">2</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step completed">3</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step current">4</div>
            <div class="progress-connector pending"></div>
            <div class="progress-step pending">5</div>
        </div>

        <div class="verification-status-container">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-white mb-4">
                    <span class="bg-gradient-to-r from-amber-400 via-yellow-400 to-orange-400 bg-clip-text text-transparent">
                        Gate Keeper Verification
                    </span>
                </h1>
                <p class="text-gray-300 max-w-2xl mx-auto">
                    Your heritage documentation is under review by our tribal council. 
                    You will be contacted within 2-3 business days.
                </p>
            </div>

            <!-- Main Status Card -->
            <div class="status-card">
                <div class="text-center">
                    <div class="gatekeeper-avatar">🛡️</div>
                    <h2 class="text-2xl font-bold text-white mb-2">Verification in Progress</h2>
                    <div class="status-indicator status-pending mb-4">
                        <span class="animate-spin mr-2">⏳</span>
                        Under Review by Tribal Council
                    </div>
                    <p class="text-gray-300 mb-6">
                        Your application to join the tribe of <strong>{{ session.get('selected_tribe', 'Unknown') }}</strong> 
                        has been submitted and is currently being reviewed by our Gate Keeper council.
                    </p>
                </div>

                <!-- Verification Timeline -->
                <div class="verification-timeline">
                    <div class="timeline-item completed">
                        <h4 class="font-bold text-white">Application Submitted</h4>
                        <p class="text-gray-300 text-sm">Your heritage documentation and tribal statement have been received.</p>
                        <p class="text-gray-400 text-xs">Just now</p>
                    </div>
                    <div class="timeline-item current">
                        <h4 class="font-bold text-white">Tribal Council Review</h4>
                        <p class="text-gray-300 text-sm">Gate Keepers are examining your documentation and heritage claims.</p>
                        <p class="text-gray-400 text-xs">In progress (1-3 business days)</p>
                    </div>
                    <div class="timeline-item">
                        <h4 class="font-bold text-white">Decision & Contact</h4>
                        <p class="text-gray-300 text-sm">You will be contacted with the verification decision.</p>
                        <p class="text-gray-400 text-xs">Pending</p>
                    </div>
                    <div class="timeline-item">
                        <h4 class="font-bold text-white">Covenant Inscription</h4>
                        <p class="text-gray-300 text-sm">Upon approval, complete your blockchain identity registration.</p>
                        <p class="text-gray-400 text-xs">Pending verification</p>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="contact-info">
                <div class="flex items-center mb-3">
                    <span class="text-2xl mr-3">📞</span>
                    <h3 class="text-xl font-bold text-emerald-100">Gate Keeper Contact</h3>
                </div>
                <p class="text-emerald-200 mb-3">
                    If you have questions about your verification status or need to provide additional documentation, 
                    you can reach our Gate Keeper council:
                </p>
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <strong class="text-emerald-100">Email:</strong>
                        <div class="text-emerald-300"><EMAIL></div>
                    </div>
                    <div>
                        <strong class="text-emerald-100">Response Time:</strong>
                        <div class="text-emerald-300">1-3 business days</div>
                    </div>
                </div>
            </div>

            <!-- Submitted Files Summary -->
            <div class="submitted-files">
                <h3 class="text-xl font-bold text-white mb-4">Submitted Documentation</h3>
                <div id="submittedFilesList">
                    <!-- Files will be loaded from sessionStorage -->
                </div>
                <div class="mt-4 p-3 bg-gray-800 rounded-lg">
                    <strong class="text-white">Tribal Statement:</strong>
                    <div class="text-gray-300 mt-2" id="tribalStatementPreview">
                        <!-- Statement will be loaded from sessionStorage -->
                    </div>
                </div>
            </div>

            <!-- What Happens Next -->
            <div class="status-card">
                <h3 class="text-xl font-bold text-white mb-4">What Happens Next?</h3>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-purple-200 mb-2">✅ If Approved</h4>
                        <ul class="text-gray-300 space-y-1 text-sm">
                            <li>• Immediate access to final registration step</li>
                            <li>• Blockchain identity inscription as verified Israelite</li>
                            <li>• Full covenant member privileges</li>
                            <li>• Access to tribal-specific resources</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold text-purple-200 mb-2">📋 If Additional Info Needed</h4>
                        <ul class="text-gray-300 space-y-1 text-sm">
                            <li>• Gate Keeper will contact you directly</li>
                            <li>• Opportunity to provide additional documentation</li>
                            <li>• Guidance on strengthening your heritage claim</li>
                            <li>• Option to register as Witness Nation instead</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Alternative Path -->
            <div class="text-center mt-8">
                <p class="text-gray-400 mb-4">
                    Want to proceed without Gate Keeper verification?
                </p>
                <a href="{{ url_for('eden_mode.witness_step2') }}" 
                   class="inline-flex items-center bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                    <span class="mr-2">🌍</span>
                    Register as Witness Nation Instead
                </a>
            </div>
        </div>
    </div>

    <!-- Refresh Status Button -->
    <div class="refresh-btn" onclick="refreshStatus()" title="Refresh verification status">
        🔄
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load submitted files from sessionStorage
    const submittedFiles = JSON.parse(sessionStorage.getItem('edenMode_verificationFiles') || '[]');
    const tribalStatement = sessionStorage.getItem('edenMode_tribalStatement') || '';
    
    const filesList = document.getElementById('submittedFilesList');
    const statementPreview = document.getElementById('tribalStatementPreview');
    
    if (submittedFiles.length > 0) {
        submittedFiles.forEach(filename => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
                <span class="text-white">📄 ${filename}</span>
                <span class="status-indicator status-pending">Submitted</span>
            `;
            filesList.appendChild(fileItem);
        });
    } else {
        filesList.innerHTML = '<div class="text-gray-400">No files recorded in session</div>';
    }
    
    if (tribalStatement) {
        statementPreview.textContent = tribalStatement.substring(0, 200) + (tribalStatement.length > 200 ? '...' : '');
    } else {
        statementPreview.textContent = 'No statement recorded in session';
    }
    
    // Check verification status periodically (simulated)
    setInterval(checkVerificationStatus, 30000); // Check every 30 seconds
});

function refreshStatus() {
    const refreshBtn = document.querySelector('.refresh-btn');
    refreshBtn.style.transform = 'rotate(360deg) scale(1.1)';
    
    // Simulate status check
    setTimeout(() => {
        refreshBtn.style.transform = 'scale(1)';
        
        // In a real implementation, this would make an API call
        // For now, we'll just show that we're checking
        console.log('Checking verification status...');
    }, 1000);
}

function checkVerificationStatus() {
    // In production, this would make an API call to check status
    // For now, we'll simulate the check
    console.log('Automatically checking verification status...');
    
    // Simulated: randomly advance to next step after some time
    const currentTime = new Date().getTime();
    const submissionTime = sessionStorage.getItem('edenMode_submissionTime');
    
    if (!submissionTime) {
        sessionStorage.setItem('edenMode_submissionTime', currentTime.toString());
        return;
    }
    
    // For demo purposes, auto-advance after 2 minutes
    if (currentTime - parseInt(submissionTime) > 120000) {
        // Simulate approval and redirect
        sessionStorage.setItem('edenMode_gatekeeperApproved', 'true');
        if (confirm('Gate Keeper verification complete! Proceed to final registration?')) {
            window.location.href = "{{ url_for('eden_mode.israelite_step5') }}";
        }
    }
}
</script>
{% endblock %}
