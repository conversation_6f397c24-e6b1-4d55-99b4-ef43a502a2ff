#!/usr/bin/env python3
"""
Route-Level Security Audit for ONNYX Platform
Systematically reviews all Flask routes for proper authorization
"""

import os
import re
import ast
from pathlib import Path

class RouteSecurityAuditor:
    def __init__(self):
        self.routes_dir = Path("web/routes")
        self.security_issues = []
        self.secure_routes = []
        self.public_routes = []
        
    def audit_all_routes(self):
        """Audit all route files for security issues"""
        print("🔍 ROUTE-LEVEL SECURITY AUDIT")
        print("=" * 60)
        
        route_files = list(self.routes_dir.glob("*.py"))
        
        for route_file in route_files:
            if route_file.name == "__init__.py":
                continue
                
            print(f"\n📁 Auditing: {route_file.name}")
            print("-" * 40)
            
            self.audit_route_file(route_file)
        
        self.generate_security_report()
    
    def audit_route_file(self, file_path):
        """Audit a specific route file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Find all route definitions
            route_pattern = r'@\w+_bp\.route\([\'"]([^\'"]+)[\'"](?:,\s*methods=\[[^\]]+\])?\)\s*(?:@\w+\s*)*\s*def\s+(\w+)'
            routes = re.findall(route_pattern, content, re.MULTILINE)
            
            for route_path, function_name in routes:
                self.analyze_route_security(file_path.name, route_path, function_name, content)
                
        except Exception as e:
            print(f"❌ Error auditing {file_path}: {e}")
    
    def analyze_route_security(self, file_name, route_path, function_name, content):
        """Analyze security of a specific route"""
        
        # Extract the function definition
        func_pattern = rf'@\w+_bp\.route.*?def\s+{function_name}.*?(?=@\w+_bp\.route|def\s+\w+|$)'
        func_match = re.search(func_pattern, content, re.DOTALL)
        
        if not func_match:
            return
            
        func_content = func_match.group(0)
        
        # Check for authentication decorators
        auth_decorators = [
            '@require_auth',
            '@require_admin', 
            '@require_permission',
            '@require_role',
            'if \'identity_id\' not in session'
        ]
        
        has_auth = any(decorator in func_content for decorator in auth_decorators)
        
        # Determine if route should be public
        public_indicators = [
            '/public/',
            'public_',
            'landing',
            'register',
            'login',
            '/api/status',
            '/api/health'
        ]
        
        is_likely_public = any(indicator in route_path.lower() or indicator in function_name.lower() 
                              for indicator in public_indicators)
        
        # Admin-only indicators
        admin_indicators = [
            'onboarding',
            'admin',
            'management',
            'genesis',
            'tribal-elder'
        ]
        
        is_likely_admin = any(indicator in route_path.lower() or indicator in function_name.lower()
                             for indicator in admin_indicators)
        
        # Categorize the route
        route_info = {
            'file': file_name,
            'path': route_path,
            'function': function_name,
            'has_auth': has_auth,
            'is_likely_public': is_likely_public,
            'is_likely_admin': is_likely_admin
        }
        
        if is_likely_public:
            self.public_routes.append(route_info)
            if has_auth:
                print(f"⚠️  WARNING: Public route has auth: {route_path}")
        elif is_likely_admin:
            if has_auth:
                self.secure_routes.append(route_info)
                print(f"✅ SECURE: Admin route protected: {route_path}")
            else:
                self.security_issues.append({**route_info, 'issue': 'Admin route without auth'})
                print(f"🚨 CRITICAL: Admin route unprotected: {route_path}")
        else:
            # Regular protected routes
            if has_auth:
                self.secure_routes.append(route_info)
                print(f"✅ SECURE: Protected route: {route_path}")
            else:
                self.security_issues.append({**route_info, 'issue': 'Protected route without auth'})
                print(f"❌ ISSUE: Unprotected route: {route_path}")
    
    def generate_security_report(self):
        """Generate comprehensive security report"""
        print("\n" + "=" * 60)
        print("📊 ROUTE SECURITY AUDIT REPORT")
        print("=" * 60)
        
        print(f"\n📈 SUMMARY:")
        print(f"  • Total Secure Routes: {len(self.secure_routes)}")
        print(f"  • Total Public Routes: {len(self.public_routes)}")
        print(f"  • Security Issues Found: {len(self.security_issues)}")
        
        if self.security_issues:
            print(f"\n🚨 CRITICAL SECURITY ISSUES:")
            print("-" * 40)
            for issue in self.security_issues:
                print(f"  ❌ {issue['file']}: {issue['path']} ({issue['function']})")
                print(f"     Issue: {issue['issue']}")
        
        print(f"\n✅ PROPERLY SECURED ROUTES:")
        print("-" * 40)
        for route in self.secure_routes[:10]:  # Show first 10
            print(f"  ✅ {route['file']}: {route['path']}")
        
        if len(self.secure_routes) > 10:
            print(f"  ... and {len(self.secure_routes) - 10} more")
        
        print(f"\n🌐 PUBLIC ROUTES (No Auth Required):")
        print("-" * 40)
        for route in self.public_routes:
            print(f"  🌐 {route['file']}: {route['path']}")
        
        print(f"\n🔧 SECURITY RECOMMENDATIONS:")
        print("-" * 40)
        
        if self.security_issues:
            print("  1. IMMEDIATE ACTION REQUIRED:")
            for issue in self.security_issues:
                if 'admin' in issue['issue'].lower():
                    print(f"     🚨 Add @require_admin to: {issue['path']}")
                else:
                    print(f"     🔒 Add @require_auth to: {issue['path']}")
        
        print("  2. GENERAL RECOMMENDATIONS:")
        print("     • Use centralized auth decorators from web.auth_decorators")
        print("     • Implement role-based access control (RBAC)")
        print("     • Add CSRF protection to forms")
        print("     • Implement rate limiting for sensitive endpoints")
        print("     • Add input validation and sanitization")
        print("     • Regular security audits and penetration testing")
        
        print(f"\n📋 SECURITY CHECKLIST:")
        print("-" * 40)
        print("  ✅ Manual onboarding routes secured")
        print("  ✅ Authentication decorators implemented")
        print("  ✅ Role-based access control system created")
        print("  ⚠️  Some routes still need security review")
        print("  ⚠️  CSRF protection needs implementation")
        print("  ⚠️  Rate limiting needs implementation")
        print("  ⚠️  Input validation needs enhancement")

def main():
    """Run the route security audit"""
    auditor = RouteSecurityAuditor()
    auditor.audit_all_routes()

if __name__ == "__main__":
    main()
