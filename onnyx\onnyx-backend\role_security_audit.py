#!/usr/bin/env python3
"""
User Role Security Audit for ONNYX Platform
Comprehensive review of role assignment, storage, verification and enforcement
"""

import json
import sqlite3
from pathlib import Path
from shared.db.db import db

class RoleSecurityAuditor:
    def __init__(self):
        self.vulnerabilities = []
        self.secure_practices = []
        self.recommendations = []
        
    def audit_role_system(self):
        """Comprehensive audit of the role system"""
        print("🔍 USER ROLE SECURITY AUDIT")
        print("=" * 60)
        
        self.audit_role_storage()
        self.audit_role_assignment()
        self.audit_role_verification()
        self.audit_role_enforcement()
        self.audit_privilege_escalation()
        self.generate_role_security_report()
    
    def audit_role_storage(self):
        """Audit how roles are stored in the database"""
        print("\n1. ROLE STORAGE AUDIT")
        print("-" * 40)
        
        try:
            # Check identities table structure
            identities = db.query("SELECT identity_id, metadata, nation_code, verification_level FROM identities LIMIT 5")
            
            print(f"✅ Found {len(identities)} sample identities")
            
            # Analyze metadata storage
            metadata_issues = 0
            for identity in identities:
                try:
                    if identity.get('metadata'):
                        metadata = json.loads(identity['metadata'])
                        
                        # Check for role-related fields
                        role_fields = ['role', 'admin_privileges', 'genesis_identity', 'badges']
                        found_fields = [field for field in role_fields if field in metadata]
                        
                        if found_fields:
                            print(f"  📊 Identity {identity['identity_id'][:8]}... has role fields: {found_fields}")
                        
                        # Check for potential security issues
                        if metadata.get('admin_privileges') and not metadata.get('genesis_identity'):
                            print(f"  ⚠️  WARNING: Admin privileges without genesis identity: {identity['identity_id'][:8]}...")
                            metadata_issues += 1
                            
                except json.JSONDecodeError:
                    print(f"  ❌ ISSUE: Invalid JSON metadata for {identity['identity_id'][:8]}...")
                    metadata_issues += 1
            
            if metadata_issues == 0:
                print("✅ SECURE: No metadata integrity issues found")
                self.secure_practices.append("Role metadata properly formatted")
            else:
                print(f"❌ ISSUES: {metadata_issues} metadata integrity problems found")
                self.vulnerabilities.append(f"Metadata integrity issues: {metadata_issues}")
                
        except Exception as e:
            print(f"❌ ERROR: Database access failed: {e}")
            self.vulnerabilities.append("Database access issues during role audit")
    
    def audit_role_assignment(self):
        """Audit how roles are assigned to users"""
        print("\n2. ROLE ASSIGNMENT AUDIT")
        print("-" * 40)
        
        # Check Genesis Identity assignment
        try:
            genesis_identities = db.query("""
                SELECT identity_id, metadata FROM identities 
                WHERE metadata LIKE '%genesis_identity%' OR metadata LIKE '%admin_privileges%'
            """)
            
            print(f"📊 Found {len(genesis_identities)} potential admin identities")
            
            admin_count = 0
            for identity in genesis_identities:
                try:
                    metadata = json.loads(identity['metadata'])
                    if metadata.get('genesis_identity') or metadata.get('admin_privileges'):
                        admin_count += 1
                        print(f"  🔑 Admin identity: {identity['identity_id'][:8]}...")
                except:
                    pass
            
            if admin_count == 1:
                print("✅ SECURE: Exactly one admin identity found")
                self.secure_practices.append("Single admin identity principle maintained")
            elif admin_count == 0:
                print("❌ CRITICAL: No admin identities found")
                self.vulnerabilities.append("No admin identities - system unmanageable")
            else:
                print(f"⚠️  WARNING: Multiple admin identities found: {admin_count}")
                self.vulnerabilities.append(f"Multiple admin identities: {admin_count}")
                
        except Exception as e:
            print(f"❌ ERROR: Admin identity audit failed: {e}")
    
    def audit_role_verification(self):
        """Audit role verification mechanisms"""
        print("\n3. ROLE VERIFICATION AUDIT")
        print("-" * 40)
        
        # Check if role integrity verification is implemented
        print("📋 Checking role integrity verification...")
        
        # Test the role determination logic
        test_cases = [
            {
                'metadata': {'admin_privileges': True, 'genesis_identity': True},
                'nation_code': 'JU',
                'verification_level': 3,
                'expected_role': 'system_admin'
            },
            {
                'metadata': {'role': 'gate_keeper'},
                'nation_code': 'JU',
                'verification_level': 2,
                'expected_role': 'gate_keeper'
            },
            {
                'metadata': {},
                'nation_code': 'JU',
                'verification_level': 1,
                'expected_role': 'israelite'
            },
            {
                'metadata': {},
                'nation_code': 'US',
                'verification_level': 1,
                'expected_role': 'witness_nation'
            },
            {
                'metadata': {},
                'nation_code': '',
                'verification_level': 0,
                'expected_role': 'observer'
            }
        ]
        
        # Import the role determination function
        try:
            from web.auth_decorators import determine_user_role, verify_role_integrity
            
            verification_passed = 0
            for i, test_case in enumerate(test_cases):
                identity = {
                    'identity_id': f'test_{i}',
                    'nation_code': test_case['nation_code'],
                    'verification_level': test_case['verification_level']
                }
                
                determined_role = determine_user_role(identity, test_case['metadata'])
                integrity_check = verify_role_integrity(identity, test_case['metadata'], determined_role)
                
                if determined_role == test_case['expected_role'] and integrity_check:
                    verification_passed += 1
                    print(f"  ✅ Test {i+1}: Role determination correct ({determined_role})")
                else:
                    print(f"  ❌ Test {i+1}: Role determination failed (got {determined_role}, expected {test_case['expected_role']})")
            
            if verification_passed == len(test_cases):
                print("✅ SECURE: Role verification logic working correctly")
                self.secure_practices.append("Role verification logic implemented correctly")
            else:
                print(f"❌ ISSUES: {len(test_cases) - verification_passed} role verification tests failed")
                self.vulnerabilities.append("Role verification logic has issues")
                
        except ImportError as e:
            print(f"❌ ERROR: Cannot import role verification functions: {e}")
            self.vulnerabilities.append("Role verification functions not accessible")
    
    def audit_role_enforcement(self):
        """Audit role enforcement in decorators and access controls"""
        print("\n4. ROLE ENFORCEMENT AUDIT")
        print("-" * 40)
        
        # Check if proper decorators are being used
        enforcement_checks = [
            "require_admin decorator implemented",
            "require_auth decorator implemented", 
            "require_permission decorator implemented",
            "require_role decorator implemented"
        ]
        
        try:
            from web.auth_decorators import require_admin, require_auth, require_permission, require_role
            
            for check in enforcement_checks:
                print(f"  ✅ {check}")
                self.secure_practices.append(check)
                
            print("✅ SECURE: All role enforcement decorators available")
            
        except ImportError as e:
            print(f"❌ ERROR: Role enforcement decorators missing: {e}")
            self.vulnerabilities.append("Role enforcement decorators not available")
    
    def audit_privilege_escalation(self):
        """Audit for privilege escalation vulnerabilities"""
        print("\n5. PRIVILEGE ESCALATION AUDIT")
        print("-" * 40)
        
        escalation_checks = [
            "Metadata tampering protection",
            "Role integrity verification",
            "Session security",
            "Database access controls"
        ]
        
        # Check for metadata tampering protection
        try:
            from web.auth_decorators import verify_role_integrity
            print("  ✅ Role integrity verification implemented")
            self.secure_practices.append("Role integrity verification")
        except:
            print("  ❌ Role integrity verification missing")
            self.vulnerabilities.append("No role integrity verification")
        
        # Check for session security
        try:
            from web.secure_auth import SecureAuth
            print("  ✅ Secure session management implemented")
            self.secure_practices.append("Secure session management")
        except:
            print("  ❌ Secure session management missing")
            self.vulnerabilities.append("No secure session management")
        
        # Check for database access controls
        print("  ⚠️  Database access controls need manual review")
        self.recommendations.append("Implement database-level role access controls")
        
        # Check for audit logging
        try:
            from web.auth_decorators import log_security_event
            print("  ✅ Security event logging implemented")
            self.secure_practices.append("Security event logging")
        except:
            print("  ❌ Security event logging missing")
            self.vulnerabilities.append("No security event logging")
    
    def generate_role_security_report(self):
        """Generate comprehensive role security report"""
        print("\n" + "=" * 60)
        print("📊 ROLE SECURITY AUDIT REPORT")
        print("=" * 60)
        
        print(f"\n📈 SUMMARY:")
        print(f"  • Secure Practices Found: {len(self.secure_practices)}")
        print(f"  • Vulnerabilities Found: {len(self.vulnerabilities)}")
        print(f"  • Recommendations: {len(self.recommendations)}")
        
        if self.vulnerabilities:
            print(f"\n🚨 VULNERABILITIES FOUND:")
            print("-" * 40)
            for vuln in self.vulnerabilities:
                print(f"  ❌ {vuln}")
        
        if self.secure_practices:
            print(f"\n✅ SECURE PRACTICES:")
            print("-" * 40)
            for practice in self.secure_practices:
                print(f"  ✅ {practice}")
        
        print(f"\n🔧 SECURITY RECOMMENDATIONS:")
        print("-" * 40)
        
        # Standard recommendations
        standard_recommendations = [
            "Implement database-level role constraints",
            "Add role change audit logging",
            "Implement role assignment approval workflow",
            "Add periodic role verification checks",
            "Implement role-based data isolation",
            "Add role expiration and renewal system",
            "Implement multi-factor authentication for admin roles",
            "Add role delegation and temporary privileges",
            "Implement role-based API rate limiting",
            "Add role hierarchy and inheritance system"
        ]
        
        all_recommendations = self.recommendations + standard_recommendations
        for rec in all_recommendations:
            print(f"  🔧 {rec}")
        
        print(f"\n📋 ROLE SECURITY CHECKLIST:")
        print("-" * 40)
        print("  ✅ Role-based access control (RBAC) system implemented")
        print("  ✅ Role verification and integrity checks in place")
        print("  ✅ Secure role storage in database metadata")
        print("  ✅ Role enforcement decorators available")
        print("  ⚠️  Database-level role constraints needed")
        print("  ⚠️  Role change audit trail needs enhancement")
        print("  ⚠️  Role delegation system needs implementation")

def main():
    """Run the role security audit"""
    auditor = RoleSecurityAuditor()
    auditor.audit_role_system()

if __name__ == "__main__":
    main()
