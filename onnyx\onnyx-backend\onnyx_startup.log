2025-07-10 15:57:24,479 - onnyx.startup - INFO - ==================================================
2025-07-10 15:57:24,489 - init_db - INFO - Existing tables: ['blocks', 'transactions', 'mempool', 'nations', 'biblical_nations', 'sqlite_sequence', 'tribal_calling_restrictions', 'identities', 'reputation', 'security_audit_log', 'secure_sessions', 'identity_passwords', 'auth_attempts', 'gate_keepers', 'voice_scroll_votes', 'api_rate_limits', 'badges', 'tokens', 'token_balances', 'token_transactions', 'selas', 'zeman_credits', 'etzem_scores', 'voice_scrolls', 'identity_verification_proposals', 'scroll_votes', 'event_logs', 'activity_ledger', 'rotation_registry', 'chain_parameters', 'rotation', 'sela_relationships', 'covenant_transactions', 'etzem_tokens', 'gleaning_pool', 'sabbath_enforcement', 'yovel_cycles', 'mining_rewards', 'labor_records', 'cipp_records']
2025-07-10 15:57:24,491 - init_db - INFO - All required tables exist
2025-07-10 15:57:24,545 - onnyx.data.db - INFO - Initialized database at C:\Users\<USER>\Documents\dev\onnyx\onnyx-backend\shared\db\db\onnyx.db
2025-07-10 15:57:24,641 - scripts.deploy_p2p_network - INFO - ==================================================
2025-07-10 15:57:24,692 - network.discovery.bootstrap - ERROR - Failed to start bootstrap node: [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8766): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-07-10 15:57:24,692 - scripts.deploy_p2p_network - WARNING - Port 8766 is in use, trying next port...
2025-07-10 15:57:24,692 - network.discovery.bootstrap - ERROR - Failed to start bootstrap node: [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8767): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-07-10 15:57:24,693 - scripts.deploy_p2p_network - WARNING - Port 8767 is in use, trying next port...
2025-07-10 15:57:24,693 - network.discovery.bootstrap - ERROR - Failed to start bootstrap node: [Errno 10048] error while attempting to bind on address ('0.0.0.0', 8768): only one usage of each socket address (protocol/network address/port) is normally permitted
2025-07-10 15:57:24,693 - scripts.deploy_p2p_network - WARNING - Port 8768 is in use, trying next port...
2025-07-10 15:57:24,693 - websockets.server - INFO - server listening on 0.0.0.0:8769
2025-07-10 15:57:24,694 - network.discovery.bootstrap - INFO - Bootstrap node started on port 8769
2025-07-10 15:57:24,694 - network.discovery.bootstrap - INFO - Initialized 12 genesis tribal elders
