#!/usr/bin/env python3
"""
Security Test Script
Test the manual onboarding access control fix
"""

import requests
import j<PERSON>

def test_unauthorized_access():
    """Test that unauthorized users cannot access manual onboarding"""
    
    base_url = "http://127.0.0.1:5000"
    
    print("🔒 TESTING MANUAL ONBOARDING SECURITY")
    print("=" * 50)
    
    # Test 1: Access onboarding dashboard without authentication
    print("\n1. Testing unauthenticated access to /onboarding/")
    try:
        response = requests.get(f"{base_url}/onboarding/", allow_redirects=False)
        if response.status_code == 302:
            print("✅ PASS: Unauthenticated access redirected (302)")
            print(f"   Redirect location: {response.headers.get('Location', 'Not specified')}")
        elif response.status_code == 401:
            print("✅ PASS: Unauthenticated access denied (401)")
        elif response.status_code == 403:
            print("✅ PASS: Unauthenticated access forbidden (403)")
        else:
            print(f"❌ FAIL: Unexpected response code: {response.status_code}")
            print(f"   Response: {response.text[:200]}...")
    except Exception as e:
        print(f"❌ ERROR: {e}")
    
    # Test 2: Access add citizen page without authentication
    print("\n2. Testing unauthenticated access to /onboarding/citizen")
    try:
        response = requests.get(f"{base_url}/onboarding/citizen", allow_redirects=False)
        if response.status_code == 302:
            print("✅ PASS: Unauthenticated access redirected (302)")
        elif response.status_code == 401:
            print("✅ PASS: Unauthenticated access denied (401)")
        elif response.status_code == 403:
            print("✅ PASS: Unauthenticated access forbidden (403)")
        else:
            print(f"❌ FAIL: Unexpected response code: {response.status_code}")
    except Exception as e:
        print(f"❌ ERROR: {e}")
    
    # Test 3: Access tribal elder page without authentication
    print("\n3. Testing unauthenticated access to /onboarding/tribal-elder")
    try:
        response = requests.get(f"{base_url}/onboarding/tribal-elder", allow_redirects=False)
        if response.status_code == 302:
            print("✅ PASS: Unauthenticated access redirected (302)")
        elif response.status_code == 401:
            print("✅ PASS: Unauthenticated access denied (401)")
        elif response.status_code == 403:
            print("✅ PASS: Unauthenticated access forbidden (403)")
        else:
            print(f"❌ FAIL: Unexpected response code: {response.status_code}")
    except Exception as e:
        print(f"❌ ERROR: {e}")
    
    # Test 4: Access bulk import page without authentication
    print("\n4. Testing unauthenticated access to /onboarding/bulk-import")
    try:
        response = requests.get(f"{base_url}/onboarding/bulk-import", allow_redirects=False)
        if response.status_code == 302:
            print("✅ PASS: Unauthenticated access redirected (302)")
        elif response.status_code == 401:
            print("✅ PASS: Unauthenticated access denied (401)")
        elif response.status_code == 403:
            print("✅ PASS: Unauthenticated access forbidden (403)")
        else:
            print(f"❌ FAIL: Unexpected response code: {response.status_code}")
    except Exception as e:
        print(f"❌ ERROR: {e}")
    
    # Test 5: API endpoint access without authentication
    print("\n5. Testing unauthenticated access to /onboarding/api/search-identity")
    try:
        response = requests.get(f"{base_url}/onboarding/api/search-identity?q=test", allow_redirects=False)
        if response.status_code == 302:
            print("✅ PASS: Unauthenticated API access redirected (302)")
        elif response.status_code == 401:
            print("✅ PASS: Unauthenticated API access denied (401)")
            try:
                error_data = response.json()
                print(f"   API Error: {error_data.get('error', 'No error message')}")
            except:
                pass
        elif response.status_code == 403:
            print("✅ PASS: Unauthenticated API access forbidden (403)")
        else:
            print(f"❌ FAIL: Unexpected response code: {response.status_code}")
    except Exception as e:
        print(f"❌ ERROR: {e}")
    
    print("\n" + "=" * 50)
    print("🔒 SECURITY TEST COMPLETE")
    print("\nIf all tests show PASS, the manual onboarding security fix is working correctly.")
    print("Observer tribe members and other unauthorized users should not be able to access these pages.")

if __name__ == "__main__":
    test_unauthorized_access()
