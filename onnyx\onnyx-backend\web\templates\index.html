{% extends "base.html" %}

{% block title %}ONNYX Platform - The Digital Backbone of Trustworthy Commerce{% endblock %}

{% block content %}
<!-- TRIBAL HERITAGE BANNER -->
<div class="w-full bg-gray-900 border-b border-cyan-500/20">
    <div class="max-w-7xl mx-auto px-4 py-3">
        <div class="flex items-center justify-center gap-4">
            <span class="text-2xl">✡️</span>
            <span class="font-orbitron font-bold text-cyan-400 text-lg">
                ONNYX TRIBAL HERITAGE PLATFORM
            </span>
            <span class="text-2xl">✡️</span>
        </div>
        <div class="text-center mt-2">
            <span class="text-purple-400/80 text-sm font-orbitron">
                "And these are the names of the children of Israel" - Genesis 46:8
            </span>
        </div>
    </div>
</div>

<!-- Hero Section with Enhanced Cyberpunk Gradient -->
<div class="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden">
    <!-- Cyber Grid Pattern Overlay -->
    <div class="absolute inset-0 opacity-30">
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-500/10 to-transparent"></div>
        <div class="absolute inset-0 bg-gradient-to-b from-transparent via-purple-500/10 to-transparent"></div>
    </div>

    <!-- Floating particles effect -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute top-1/4 left-1/4 w-2 h-2 bg-cyan-400 rounded-full animate-ping"></div>
        <div class="absolute top-1/3 right-1/3 w-1 h-1 bg-purple-400 rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce"></div>
        <div class="absolute top-2/3 right-1/4 w-1 h-1 bg-cyan-400 rounded-full animate-ping"></div>
        <div class="absolute top-1/2 left-1/2 w-1 h-1 bg-purple-400 rounded-full animate-pulse"></div>
        <div class="absolute bottom-1/3 right-1/2 w-1.5 h-1.5 bg-blue-400 rounded-full animate-bounce"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 relative z-10">
        <div class="text-center">
            <!-- Hero Logo -->
            <div class="mb-8 flex justify-center">
                <div class="flex items-center justify-center group">
                    <img src="{{ url_for('static', filename='images/onnyx_logo.png') }}"
                         alt="ONNYX Logo"
                         class="w-20 h-20 md:w-24 md:h-24 object-contain transition-transform duration-300 group-hover:scale-110"
                         onerror="console.log('Logo failed to load:', this.src); this.style.display='none'; this.nextElementSibling.style.display='block';"
                         onload="console.log('Logo loaded successfully:', this.src);">
                    <!-- Fallback symbol only if image fails -->
                    <span class="text-6xl md:text-7xl font-black text-cyan-400" style="display: none; text-shadow: 0 0 20px rgba(0, 255, 247, 0.8);">⬢</span>
                </div>
            </div>
                    <span class="text-6xl md:text-7xl font-black text-cyber-cyan" style="display: none; text-shadow: 0 0 20px rgba(0, 255, 247, 0.8);">⬢</span>
                </div>
            </div>

            <!-- Main Heading with Hologram Effect and Spinning N's -->
            <h1 class="text-6xl md:text-8xl font-orbitron font-bold mb-8 text-white">
                <span class="bg-gradient-to-r from-cyan-400 via-purple-400 to-cyan-400 bg-clip-text text-transparent animate-pulse">
                    O<span class="relative inline-block transform transition-transform duration-700 hover:rotate-12 hover:scale-110" title="First Stone">N</span><span class="relative inline-block transform transition-transform duration-700 hover:rotate-12 hover:scale-110" title="Second Stone">N</span>YX
                </span>
            </h1>

            <!-- Tribal Heritage Indicator -->
            <div class="mb-6">
                <div class="flex items-center justify-center gap-4">
                    <span class="text-purple-400 text-2xl">✡️</span>
                    <span class="text-cyan-400 font-orbitron text-lg">The Twelve Tribes of Israel</span>
                    <span class="text-purple-400 text-2xl">✡️</span>
                </div>
                <p class="text-purple-400/80 text-sm mt-2 font-orbitron">Hover over the N's to see the tribal names on the onyx stones</p>
            </div>

            <h2 class="text-2xl md:text-4xl font-orbitron font-medium mb-6 text-cyan-400">
                Reclaim Your Biblical Identity — Register Your Lineage
            </h2>
            <p class="text-lg md:text-xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
                Discover your true covenant identity through biblical lineage verification. Join the 12 Tribes of Israel,
                witness nations, and covenant community on the world's first blockchain built on divine economic principles.
            </p>

            <!-- Enhanced Value Proposition Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 max-w-6xl mx-auto">
                <div class="bg-gray-800/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-700/50 text-center hover:bg-gray-800/70 transition-all duration-300">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-2xl flex items-center justify-center text-3xl">
                        <span class="text-4xl">📜</span>
                    </div>
                    <h3 class="font-orbitron font-bold text-cyan-400 mb-3 text-2xl md:text-3xl">Biblical Identity</h3>
                    <p class="text-gray-300 leading-relaxed text-lg md:text-xl">Discover your true covenant lineage through the 12 Tribes of Israel and witness nations.</p>
                </div>
                <div class="bg-gray-800/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-700/50 text-center hover:bg-gray-800/70 transition-all duration-300">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-purple-400 to-blue-500 rounded-2xl flex items-center justify-center text-3xl">
                        <span class="text-4xl">⚖️</span>
                    </div>
                    <h3 class="font-orbitron font-bold text-purple-400 mb-3 text-2xl md:text-3xl">Gate Keeper Verification</h3>
                    <p class="text-gray-300 leading-relaxed text-lg md:text-xl">Council of 12 Gate Keepers verify Israelite covenant identities through sacred voting.</p>
                </div>
                <div class="bg-gray-800/50 backdrop-blur-sm p-8 rounded-2xl border border-gray-700/50 text-center hover:bg-gray-800/70 transition-all duration-300">
                    <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-400 to-green-500 rounded-2xl flex items-center justify-center text-3xl">
                        <span class="text-4xl">💎</span>
                    </div>
                    <h3 class="font-orbitron font-bold text-blue-400 mb-3 text-2xl md:text-3xl">Sacred Economics</h3>
                    <p class="text-gray-300 leading-relaxed text-lg md:text-xl">Biblical tokenomics with Sabbath cycles, Yovel jubilee, and anti-usury covenant principles.</p>
                </div>
            </div>

            <!-- Platform Statistics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 text-center p-8 hover:bg-gray-800/70 transition-all duration-300">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">👤</span>
                    </div>
                    <div class="text-4xl md:text-5xl font-orbitron font-bold text-cyan-400 mb-3">{{ platform_stats.identities }}</div>
                    <div class="text-base font-semibold text-gray-300 uppercase tracking-wider">Verified Identities</div>
                </div>
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 text-center p-8 hover:bg-gray-800/70 transition-all duration-300">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🏢</span>
                    </div>
                    <div class="text-4xl md:text-5xl font-orbitron font-bold text-purple-400 mb-3">{{ platform_stats.selas }}</div>
                    <div class="text-base font-semibold text-gray-300 uppercase tracking-wider">Active Validators</div>
                </div>
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 text-center p-8 hover:bg-gray-800/70 transition-all duration-300">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🔄</span>
                    </div>
                    <div class="text-4xl md:text-5xl font-orbitron font-bold text-blue-400 mb-3">{{ platform_stats.transactions }}</div>
                    <div class="text-base font-semibold text-gray-300 uppercase tracking-wider">Total Transactions</div>
                </div>
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-xl border border-gray-700/50 text-center p-8 hover:bg-gray-800/70 transition-all duration-300">
                    <div class="w-12 h-12 mx-auto mb-4 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                        <span class="text-2xl">🟩</span>
                    </div>
                    <div class="text-4xl md:text-5xl font-orbitron font-bold text-green-400 mb-3">{{ platform_stats.blocks }}</div>
                    <div class="text-base font-semibold text-gray-300 uppercase tracking-wider">Blocks Secured</div>
                </div>
            </div>

            <!-- Call to Action Buttons -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 justify-center items-center mb-16 max-w-5xl mx-auto">
                <!-- Start Registration Button -->
                <a href="{{ url_for('eden_mode.step1') }}"
                   class="bg-gradient-to-r from-cyan-400 to-blue-500 px-6 py-4 rounded-xl font-orbitron font-semibold text-lg text-white text-center transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-cyan-400/30">
                    <span class="flex items-center justify-center gap-3">
                        <span class="text-xl">📜</span>
                        <span>Start Registration</span>
                    </span>
                </a>

                <!-- Learn About Tribes Button -->
                <a href="/tribes/overview"
                   class="bg-black border-2 border-purple-400 px-6 py-4 rounded-xl font-orbitron font-semibold text-lg text-purple-400 text-center transition-all duration-300 hover:scale-105 hover:bg-purple-400/10 hover:shadow-lg hover:shadow-purple-400/30">
                    <span class="flex items-center justify-center gap-3">
                        <span class="text-xl">🏛️</span>
                        <span>Learn About Tribes</span>
                    </span>
                </a>

                <!-- Community Governance Button -->
                <a href="/governance/public"
                   class="bg-black border-2 border-blue-400 px-6 py-4 rounded-xl font-orbitron font-semibold text-lg text-blue-400 text-center transition-all duration-300 hover:scale-105 hover:bg-blue-400/10 hover:shadow-lg hover:shadow-blue-400/30">
                    <span class="flex items-center justify-center gap-3">
                        <span class="text-xl">⚖️</span>
                        <span>Community Governance</span>
                    </span>
                </a>

                <!-- FAQ Button -->
                <a href="/faq/biblical-blockchain"
                   class="bg-black border-2 border-green-400 px-6 py-4 rounded-xl font-orbitron font-semibold text-lg text-green-400 text-center transition-all duration-300 hover:scale-105 hover:bg-green-400/10 hover:shadow-lg hover:shadow-green-400/30">
                    <span class="flex items-center justify-center gap-3">
                        <span class="text-xl">❓</span>
                        <span>FAQ</span>
                    </span>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Core Technologies Section with Enhanced Design -->
<div class="py-24 relative overflow-hidden">
    <!-- Background effects -->
    <div class="absolute inset-0 bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Section Header -->
        <div class="text-center mb-20">
            <div class="flex justify-center mb-8">
                <!-- Blockchain Creation Animation -->
                <div class="relative w-24 h-24">
                    <!-- Central blockchain node -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="w-8 h-8 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-lg animate-pulse shadow-lg shadow-cyber-cyan/50"></div>
                    </div>

                    <!-- Connecting nodes -->
                    <div class="absolute top-2 left-2 w-4 h-4 bg-cyber-purple rounded-md animate-ping opacity-70"></div>
                    <div class="absolute top-2 right-2 w-4 h-4 bg-cyber-green rounded-md animate-bounce opacity-70"></div>
                    <div class="absolute bottom-2 left-2 w-4 h-4 bg-cyber-blue rounded-md animate-pulse opacity-70"></div>
                    <div class="absolute bottom-2 right-2 w-4 h-4 bg-cyber-cyan rounded-md animate-ping opacity-70"></div>

                    <!-- Connection lines -->
                    <div class="absolute inset-0 opacity-40">
                        <svg class="w-full h-full" viewBox="0 0 96 96">
                            <line x1="16" y1="16" x2="48" y2="48" stroke="currentColor" stroke-width="1" class="text-cyber-cyan animate-pulse"/>
                            <line x1="80" y1="16" x2="48" y2="48" stroke="currentColor" stroke-width="1" class="text-cyber-purple animate-pulse"/>
                            <line x1="16" y1="80" x2="48" y2="48" stroke="currentColor" stroke-width="1" class="text-cyber-blue animate-pulse"/>
                            <line x1="80" y1="80" x2="48" y2="48" stroke="currentColor" stroke-width="1" class="text-cyber-green animate-pulse"/>
                        </svg>
                    </div>
                </div>
            </div>
            <h2 class="text-5xl md:text-6xl font-orbitron font-bold text-cyber-cyan mb-6">
                Core Technologies
            </h2>
            <p class="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
                Advanced cryptographic protocols powering the future of verified business operations
            </p>
        </div>

        <!-- Technology Cards Grid -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <!-- Quantum-Resistant Cryptography -->
            <div class="glass-card-enhanced p-8 group hover:scale-105 transition-all duration-500 tech-card-animated quantum-security-card">
                <div class="text-center mb-6">
                    <div class="w-20 h-20 mx-auto bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-2xl flex items-center justify-center group-hover:shadow-xl group-hover:shadow-cyber-cyan/40 transition-all duration-500 mb-4">
                        <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-4" style="text-shadow: 0 0 20px rgba(0, 255, 247, 0.5);">
                        Quantum-Resistant Security
                    </h3>
                </div>
                <p class="text-text-secondary leading-relaxed text-center mb-6">
                    Advanced ECDSA cryptographic identities with quantum-resistant algorithms ensuring long-term security for business operations.
                </p>
                <div class="flex items-center justify-center space-x-2">
                    <div class="w-3 h-3 bg-cyber-cyan rounded-full animate-pulse"></div>
                    <span class="text-sm text-cyber-cyan font-mono uppercase tracking-wider">ACTIVE PROTOCOL</span>
                </div>
            </div>

            <!-- Etzem Trust Engine -->
            <div class="glass-card-enhanced p-8 group hover:scale-105 transition-all duration-500 tech-card-animated trust-protocol-card">
                <div class="text-center mb-6">
                    <div class="w-20 h-20 mx-auto bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-2xl flex items-center justify-center group-hover:shadow-xl group-hover:shadow-cyber-purple/40 transition-all duration-500 mb-4">
                        <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-orbitron font-bold text-cyber-purple mb-4" style="text-shadow: 0 0 20px rgba(154, 0, 255, 0.5);">
                        Etzem Trust Protocol
                    </h3>
                </div>
                <p class="text-text-secondary leading-relaxed text-center mb-6">
                    Algorithmic reputation system analyzing transaction patterns, community endorsements, and business performance metrics.
                </p>
                <div class="flex items-center justify-center space-x-2">
                    <div class="w-3 h-3 bg-cyber-purple rounded-full animate-pulse"></div>
                    <span class="text-sm text-cyber-purple font-mono uppercase tracking-wider">TRUST ENGINE</span>
                </div>
            </div>

            <!-- Mikvah Token System -->
            <div class="glass-card-enhanced p-8 group hover:scale-105 transition-all duration-500 tech-card-animated token-economy-card">
                <div class="text-center mb-6">
                    <div class="w-20 h-20 mx-auto bg-gradient-to-br from-cyber-blue to-green-400 rounded-2xl flex items-center justify-center group-hover:shadow-xl group-hover:shadow-cyber-blue/40 transition-all duration-500 mb-4">
                        <svg class="w-10 h-10 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-orbitron font-bold text-cyber-blue mb-4" style="text-shadow: 0 0 20px rgba(0, 128, 255, 0.5);">
                        Mikvah Token Economy
                    </h3>
                </div>
                <p class="text-text-secondary leading-relaxed text-center mb-6">
                    Decentralized token minting system enabling businesses to create loyalty programs, equity shares, and community governance tokens.
                </p>
                <div class="flex items-center justify-center space-x-2">
                    <div class="w-3 h-3 bg-cyber-blue rounded-full animate-pulse"></div>
                    <span class="text-sm text-cyber-blue font-mono uppercase tracking-wider">TOKEN FACTORY</span>
                </div>
            </div>
        </div>

        <!-- Enhanced Call to Action for Core Technologies -->
        <div class="text-center">
            <div class="glass-card-premium p-8 max-w-2xl mx-auto">
                <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-4">Experience the Technology</h3>
                <p class="text-text-secondary mb-6 leading-relaxed">
                    See these advanced protocols in action. Join the network and experience quantum-secure, trust-verified blockchain operations.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ url_for('explorer.index') }}"
                       class="glass-button-primary px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                        🔍 Explore Blockchain
                    </a>
                    <a href="{{ url_for('tokenomics.overview') }}"
                       class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                        📊 View Tokenomics
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Active Validators Network -->
{% if recent_selas %}
<div class="py-20 relative">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-16">
            <div>
                <h2 class="text-4xl font-orbitron font-bold mb-4">
                    <span class="hologram-text">Active Validator Network</span>
                </h2>
                <p class="text-xl text-gray-400">Verified business validators securing the network</p>
            </div>
            <a href="{{ url_for('sela.directory') }}"
               class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                View All Validators →
            </a>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for sela in recent_selas[:6] %}
            <div class="glass-card p-6 neuro-card hover:scale-105 transition-all duration-300 group data-stream">
                <div class="flex items-start justify-between mb-6">
                    <div class="flex-1">
                        <h3 class="text-xl font-orbitron font-bold text-white mb-2">
                            <a href="{{ url_for('sela.profile', sela_id=sela.sela_id) }}"
                               class="hover:text-cyber-cyan transition-colors duration-300">
                                {{ sela.name }}
                            </a>
                        </h3>
                        <p class="text-sm text-gray-400 uppercase tracking-wider">{{ sela.category }}</p>
                    </div>
                    <span class="badge-success">
                        ACTIVE
                    </span>
                </div>

                <div class="space-y-3 mb-6">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Validator ID</span>
                        <span class="text-sm font-mono text-cyber-cyan">{{ sela.sela_id[:8] }}...</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Owner</span>
                        <span class="text-sm text-gray-300">{{ sela.owner_name }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">Registered</span>
                        <span class="text-sm text-gray-300">{{ sela.created_at|format_timestamp }}</span>
                    </div>
                </div>

                <div class="w-full h-1 bg-gradient-to-r from-transparent via-cyber-cyan to-transparent"></div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- Blockchain Activity Stream -->
{% if recent_transactions %}
<div class="py-20 relative">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center mb-16">
            <div>
                <h2 class="text-4xl font-orbitron font-bold mb-4">
                    <span class="hologram-text">Live Transaction Stream</span>
                </h2>
                <p class="text-xl text-gray-400">Real-time blockchain activity monitoring</p>
            </div>
            <a href="{{ url_for('explorer.transactions') }}"
               class="glass-button px-6 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                Launch Explorer →
            </a>
        </div>

        <div class="glass-card neuro-card overflow-hidden">
            <div class="px-8 py-6 border-b border-white/10 bg-gradient-to-r from-cyber-cyan/10 to-cyber-purple/10">
                <h3 class="text-xl font-orbitron font-bold text-cyber-cyan">Recent Transactions</h3>
            </div>
            <div class="divide-y divide-white/5">
                {% for tx in recent_transactions[:5] %}
                <div class="px-8 py-6 hover:bg-white/5 transition-all duration-300 data-stream">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-6">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl flex items-center justify-center">
                                    <svg class="w-6 h-6 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <p class="text-lg font-orbitron font-semibold text-white">{{ tx.op }}</p>
                                <p class="text-sm font-mono text-cyber-cyan">{{ tx.tx_id|truncate_hash }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <span class="badge-success">{{ tx.status|title }}</span>
                            <p class="text-sm text-gray-400 mt-2">{{ tx.created_at|format_timestamp }}</p>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- User Journey Section -->
<div class="py-20 relative">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-4xl font-orbitron font-bold mb-12 text-center">
            <span class="hologram-text">Your Path to Digital Freedom</span>
        </h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <!-- Step 1 -->
            <div class="glass-card p-8 text-center relative group hover:scale-105 transition-all duration-300 journey-card-animated covenant-awakening-card">
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-cyber-cyan rounded-full flex items-center justify-center font-orbitron font-bold text-onyx-black">1</div>
                <div class="text-4xl mb-4 mt-4">🔐</div>
                <h3 class="font-orbitron font-bold text-cyber-cyan mb-3">Covenant Awakening</h3>
                <p class="text-text-secondary mb-6">Begin your spiritual journey through Eden Mode. Discover your true covenant identity.</p>
                {% if not current_user %}
                <a href="{{ url_for('eden_mode.step1') }}" class="glass-button-primary px-6 py-2 rounded-lg font-semibold">Let's Begin</a>
                {% else %}
                <div class="text-cyber-green font-semibold">✅ Completed</div>
                {% endif %}
            </div>

            <!-- Step 2 -->
            <div class="glass-card p-8 text-center relative group hover:scale-105 transition-all duration-300 journey-card-animated business-registration-card">
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-cyber-purple rounded-full flex items-center justify-center font-orbitron font-bold text-onyx-black">2</div>
                <div class="text-4xl mb-4 mt-4">🏢</div>
                <h3 class="font-orbitron font-bold text-cyber-purple mb-3">Register Business</h3>
                <p class="text-text-secondary mb-6">Become a verified validator. Earn rewards for honest commerce.</p>
                {% if current_user %}
                <a href="{{ url_for('eden_mode.step4') }}" class="glass-button px-6 py-2 rounded-lg font-semibold">Create Sela</a>
                {% else %}
                <div class="text-text-muted font-semibold">Complete Eden Mode First</div>
                {% endif %}
            </div>

            <!-- Step 3 -->
            <div class="glass-card p-8 text-center relative group hover:scale-105 transition-all duration-300 journey-card-animated rewards-earning-card">
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-cyber-blue rounded-full flex items-center justify-center font-orbitron font-bold text-onyx-black">3</div>
                <div class="text-4xl mb-4 mt-4">⭐</div>
                <h3 class="font-orbitron font-bold text-cyber-blue mb-3">Earn Rewards</h3>
                <p class="text-text-secondary mb-6">Mine blocks, build reputation, and earn through biblical tokenomics.</p>
                <a href="{{ url_for('tokenomics.overview') }}" class="glass-button px-6 py-2 rounded-lg font-semibold">Learn More</a>
            </div>
        </div>


    </div>
</div>

<!-- ONNYX Development Roadmap Section -->
<div class="py-32 relative overflow-hidden">
    <!-- Enhanced Background effects -->
    <div class="absolute inset-0 bg-gradient-to-br from-onyx-black via-onyx-dark to-onyx-black"></div>

    Enhanced floating blockchain nodes background
    <div class="absolute inset-0 overflow-hidden floating-blockchain-nodes">
        <!-- Top section dots -->
        <div class="absolute top-1/6 left-1/5 w-3 h-3 bg-cyber-cyan rounded-full animate-pulse opacity-30"></div>
        <div class="absolute top-1/4 right-1/4 w-2 h-2 bg-cyber-purple rounded-full animate-ping opacity-25"></div>
        <div class="absolute top-1/3 left-2/3 w-2.5 h-2.5 bg-cyber-blue rounded-full animate-bounce opacity-20"></div>

        <!-- Middle section dots -->
        <div class="absolute top-1/2 left-1/6 w-2 h-2 bg-cyber-green rounded-full animate-pulse opacity-35"></div>
        <div class="absolute top-1/2 right-1/5 w-3 h-3 bg-cyber-cyan rounded-full animate-ping opacity-30"></div>
        <div class="absolute top-3/5 left-3/4 w-2.5 h-2.5 bg-cyber-purple rounded-full animate-bounce opacity-25"></div>

        <!-- Bottom section dots -->
        <div class="absolute bottom-1/3 left-1/4 w-2 h-2 bg-cyber-blue rounded-full animate-pulse opacity-30"></div>
        <div class="absolute bottom-1/4 right-1/3 w-3 h-3 bg-cyber-green rounded-full animate-ping opacity-35"></div>
        <div class="absolute bottom-1/5 left-1/2 w-2.5 h-2.5 bg-cyber-cyan rounded-full animate-bounce opacity-25"></div>

        <!-- Additional scattered dots -->
        <div class="absolute top-3/4 right-2/3 w-1.5 h-1.5 bg-cyber-purple rounded-full animate-pulse opacity-20"></div>
        <div class="absolute top-1/8 left-1/2 w-2 h-2 bg-cyber-blue rounded-full animate-ping opacity-30"></div>
        <div class="absolute bottom-2/5 right-1/6 w-2.5 h-2.5 bg-cyber-green rounded-full animate-bounce opacity-25"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <!-- Enhanced Section Header -->
        <div class="text-center mb-24">
            <div class="flex justify-center mb-8">
                <div class="glass-card-premium w-24 h-24 rounded-3xl flex items-center justify-center relative group roadmap-icon-container">
                    <!-- Custom ONNYX Roadmap Icon -->
                    <div class="relative">
                        <!-- Outer hexagon -->
                        <svg class="w-12 h-12 text-cyber-cyan absolute inset-0 group-hover:rotate-180 transition-transform duration-1000" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                        </svg>

                        <!-- Connecting lines -->
                        <div class="absolute inset-0 opacity-60">
                            <div class="absolute top-1/2 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-cyber-cyan to-transparent transform -translate-y-0.5"></div>
                            <div class="absolute left-1/2 top-0 h-full w-0.5 bg-gradient-to-b from-transparent via-cyber-cyan to-transparent transform -translate-x-0.5"></div>
                        </div>
                    </div>

                    <!-- Animated glow effect -->
                    <div class="absolute inset-0 rounded-3xl bg-gradient-to-r from-cyber-cyan/20 to-cyber-purple/20 animate-pulse"></div>
                </div>
            </div>
            <h2 class="text-5xl md:text-7xl font-orbitron font-bold mb-6">
                <span class="bg-gradient-to-r from-cyber-cyan via-cyber-purple to-cyber-blue bg-clip-text text-transparent">
                    ONNYX Roadmap
                </span>
            </h2>
            <p class="text-xl text-text-secondary max-w-4xl mx-auto leading-relaxed mb-8">
                The evolutionary path of covenant-based blockchain technology, merging ancient wisdom with quantum-secure infrastructure
            </p>

            <!-- Roadmap Progress Indicator -->
            <div class="flex justify-center items-center space-x-4 mb-8 roadmap-progress-indicator">
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-cyber-green rounded-full animate-pulse"></div>
                    <span class="text-sm text-cyber-green font-orbitron font-semibold">FOUNDATION COMPLETE</span>
                </div>
                <div class="w-8 h-0.5 bg-gradient-to-r from-cyber-green to-cyber-cyan"></div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-cyber-cyan rounded-full animate-ping"></div>
                    <span class="text-sm text-cyber-cyan font-orbitron font-semibold">TOKENOMICS ACTIVE</span>
                </div>
                <div class="w-8 h-0.5 bg-gradient-to-r from-cyber-cyan to-cyber-purple opacity-50"></div>
                <div class="flex items-center space-x-2">
                    <div class="w-3 h-3 bg-cyber-purple rounded-full opacity-50"></div>
                    <span class="text-sm text-cyber-purple font-orbitron font-semibold opacity-50">ADVANCED PENDING</span>
                </div>
            </div>
        </div>

        <!-- Enhanced Roadmap Timeline -->
        <div class="relative">
            <!-- Central Timeline Line with Enhanced Design -->
            <div class="absolute left-1/2 transform -translate-x-1 w-1 h-full bg-gradient-to-b from-cyber-cyan via-cyber-purple to-cyber-green rounded-full shadow-lg shadow-cyber-cyan/30"></div>

            <div class="space-y-32">
                <!-- Phase 1: Foundation (Completed) -->
                <div class="flex items-center relative">
                    <!-- Timeline Node for Phase 1 -->
                    <div class="absolute left-1/2 transform -translate-x-3 w-6 h-6 bg-cyber-green rounded-full shadow-lg shadow-cyber-green/50 animate-pulse border-4 border-onyx-black z-20 roadmap-timeline-node"></div>

                    <div class="w-1/2 pr-8 text-right">
                        <div class="glass-card-enhanced p-8 roadmap-card-animated foundation-phase-card border-l-4 border-cyber-green">
                            <div class="flex items-center justify-end space-x-4 mb-6">
                                <div class="text-right">
                                    <h3 class="text-3xl font-orbitron font-bold text-cyber-green mb-2">Genesis Foundation</h3>
                                    <p class="text-sm text-cyber-green/80 font-mono uppercase tracking-wider">COVENANT ESTABLISHED</p>
                                </div>
                            </div>
                            <div class="space-y-3 text-text-secondary">
                                <div class="flex items-center justify-end space-x-3 group">
                                    <span class="group-hover:text-cyber-green transition-colors">⬢ Quantum-resistant blockchain core</span>
                                    <div class="w-2 h-2 bg-cyber-green rounded-full"></div>
                                </div>
                                <div class="flex items-center justify-end space-x-3 group">
                                    <span class="group-hover:text-cyber-green transition-colors">⬢ Eden Mode identity awakening</span>
                                    <div class="w-2 h-2 bg-cyber-green rounded-full"></div>
                                </div>
                                <div class="flex items-center justify-end space-x-3 group">
                                    <span class="group-hover:text-cyber-green transition-colors">⬢ Sela validator network</span>
                                    <div class="w-2 h-2 bg-cyber-green rounded-full"></div>
                                </div>
                                <div class="flex items-center justify-end space-x-3 group">
                                    <span class="group-hover:text-cyber-green transition-colors">⬢ CIPP covenant protection</span>
                                    <div class="w-2 h-2 bg-cyber-green rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 pl-8">
                        <div class="text-sm text-cyber-green font-orbitron font-bold">COMPLETED</div>
                        <div class="text-xs text-text-tertiary">Q4 2024</div>
                    </div>
                </div>

                <!-- Phase 2: Biblical Tokenomics (Current) -->
                <div class="flex items-center relative">
                    <!-- Timeline Node for Phase 2 -->
                    <div class="absolute left-1/2 transform -translate-x-3 w-6 h-6 bg-cyber-cyan rounded-full shadow-lg shadow-cyber-cyan/50 animate-ping border-4 border-onyx-black z-20 roadmap-timeline-node"></div>

                    <div class="w-1/2 pr-8 text-right">
                        <div class="text-sm text-cyber-cyan font-orbitron font-bold">IN PROGRESS</div>
                        <div class="text-xs text-text-tertiary">Q1 2025</div>
                    </div>

                    <div class="w-1/2 pl-8">
                        <div class="glass-card-enhanced p-8 roadmap-card-animated tokenomics-phase-card border-l-4 border-cyber-cyan">
                            <div class="flex items-center space-x-6 mb-6">

                                <div class="flex-1">
                                    <h3 class="text-3xl font-orbitron font-bold text-cyber-cyan mb-2">Sacred Economics</h3>
                                    <p class="text-sm text-cyber-cyan/80 font-mono uppercase tracking-wider">BIBLICAL PROTOCOLS ACTIVE</p>
                                </div>
                            </div>
                            <div class="space-y-3 text-text-secondary">
                                <div class="flex items-center space-x-3 group">
                                    <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse"></div>
                                    <span class="group-hover:text-cyber-cyan transition-colors">⚡ Yovel jubilee cycles (50-year resets)</span>
                                </div>
                                <div class="flex items-center space-x-3 group">
                                    <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse"></div>
                                    <span class="group-hover:text-cyber-cyan transition-colors">⚡ Sabbath mining enforcement</span>
                                </div>
                                <div class="flex items-center space-x-3 group">
                                    <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse"></div>
                                    <span class="group-hover:text-cyber-cyan transition-colors">⚡ Gleaning pools for community</span>
                                </div>
                                <div class="flex items-center space-x-3 group">
                                    <div class="w-2 h-2 bg-cyber-cyan rounded-full animate-pulse"></div>
                                    <span class="group-hover:text-cyber-cyan transition-colors">⚡ Anti-usury covenant lending</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Phase 3: Advanced Features (Planned) -->
                <div class="flex items-center relative">
                    <!-- Timeline Node for Phase 3 -->
                    <div class="absolute left-1/2 transform -translate-x-3 w-6 h-6 bg-cyber-purple rounded-full shadow-lg shadow-cyber-purple/50 border-4 border-onyx-black z-20 roadmap-timeline-node"></div>

                    <div class="w-1/2 pr-8 text-right">
                        <div class="glass-card-enhanced p-8 roadmap-card-animated advanced-features-card border-l-4 border-cyber-purple">
                            <div class="flex items-center justify-end space-x-4 mb-6">
                                <div class="text-right">
                                    <h3 class="text-3xl font-orbitron font-bold text-cyber-purple mb-2">Neural Covenant</h3>
                                    <p class="text-sm text-cyber-purple/80 font-mono uppercase tracking-wider">AI PROPHECY INTEGRATION</p>
                                </div>
                                <div class="w-10 h-10 bg-cyber-purple rounded-full flex items-center justify-center shadow-lg shadow-cyber-purple/50">
                                    <svg class="w-5 h-5 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="space-y-3 text-text-secondary">
                                <div class="flex items-center justify-end space-x-3 group">
                                    <span class="group-hover:text-cyber-purple transition-colors">⚡ Quantum mobile interfaces</span>
                                    <div class="w-2 h-2 bg-cyber-purple rounded-full"></div>
                                </div>
                                <div class="flex items-center justify-end space-x-3 group">
                                    <span class="group-hover:text-cyber-purple transition-colors">⚡ Multi-chain covenant bridges</span>
                                    <div class="w-2 h-2 bg-cyber-purple rounded-full"></div>
                                </div>
                                <div class="flex items-center justify-end space-x-3 group">
                                    <span class="group-hover:text-cyber-purple transition-colors">⚡ Prophetic AI compliance</span>
                                    <div class="w-2 h-2 bg-cyber-purple rounded-full"></div>
                                </div>
                                <div class="flex items-center justify-end space-x-3 group">
                                    <span class="group-hover:text-cyber-purple transition-colors">⚡ Tribal elder governance</span>
                                    <div class="w-2 h-2 bg-cyber-purple rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="w-20 h-20 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-3xl flex items-center justify-center relative z-10 border-4 border-onyx-black shadow-xl shadow-cyber-purple/40 group-hover:scale-105 transition-all duration-300 ml-8">
                        <!-- Custom Advanced Features Icon -->
                        <svg class="w-10 h-10 text-onyx-black" fill="currentColor" viewBox="0 0 24 24">
                            <circle cx="6" cy="6" r="2"/>
                            <circle cx="18" cy="6" r="2"/>
                            <circle cx="6" cy="18" r="2"/>
                            <circle cx="18" cy="18" r="2"/>
                            <circle cx="12" cy="12" r="3"/>
                            <path d="M8 6l2.5 4.5M16 6l-2.5 4.5M8 18l2.5-4.5M16 18l-2.5-4.5" stroke="currentColor" stroke-width="1.5" fill="none"/>
                        </svg>
                    </div>
                    <div class="w-1/2 pl-8">
                        <div class="text-sm text-cyber-purple font-orbitron font-bold">PLANNED</div>
                        <div class="text-xs text-text-tertiary">Q2 2025</div>
                    </div>
                </div>

                <!-- Phase 4: Global Expansion (Future) -->
                <div class="flex items-center relative">
                    <!-- Timeline Node for Phase 4 -->
                    <div class="absolute left-1/2 transform -translate-x-3 w-6 h-6 bg-cyber-blue rounded-full shadow-lg shadow-cyber-blue/50 border-4 border-onyx-black z-20 roadmap-timeline-node"></div>

                    <div class="w-1/2 pr-8 text-right">
                        <div class="text-sm text-cyber-blue font-orbitron font-bold">FUTURE</div>
                        <div class="text-xs text-text-tertiary">Q3 2025</div>
                    </div>
                    <div class="w-20 h-20 bg-gradient-to-br from-cyber-blue to-green-400 rounded-3xl flex items-center justify-center relative z-10 border-4 border-onyx-black shadow-xl shadow-cyber-blue/40 group-hover:scale-105 transition-all duration-300 mr-8">
                        <!-- Custom Global Expansion Icon -->
                        <svg class="w-10 h-10 text-onyx-black" fill="currentColor" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" fill="none" stroke="currentColor" stroke-width="1.5"/>
                            <path d="M2 12h20M12 2a15.3 15.3 0 014 10 15.3 15.3 0 01-4 10 15.3 15.3 0 01-4-10 15.3 15.3 0 014-10z" fill="none" stroke="currentColor" stroke-width="1"/>
                        </svg>
                    </div>
                    <div class="w-1/2 pl-8">
                        <div class="glass-card-enhanced p-8 roadmap-card-animated global-expansion-card border-l-4 border-cyber-blue">
                            <div class="flex items-center space-x-4 mb-6">
                                <div class="w-10 h-10 bg-cyber-blue rounded-full flex items-center justify-center shadow-lg shadow-cyber-blue/50">
                                    <svg class="w-5 h-5 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-3xl font-orbitron font-bold text-cyber-blue mb-2">Global Covenant</h3>
                                    <p class="text-sm text-cyber-blue/80 font-mono uppercase tracking-wider">WORLDWIDE AWAKENING</p>
                                </div>
                            </div>
                            <div class="space-y-3 text-text-secondary">
                                <div class="flex items-center space-x-3 group">
                                    <div class="w-2 h-2 bg-cyber-blue rounded-full"></div>
                                    <span class="group-hover:text-cyber-blue transition-colors">⚡ 47 nation covenant network</span>
                                </div>
                                <div class="flex items-center space-x-3 group">
                                    <div class="w-2 h-2 bg-cyber-blue rounded-full"></div>
                                    <span class="group-hover:text-cyber-blue transition-colors">⚡ Enterprise covenant adoption</span>
                                </div>
                                <div class="flex items-center space-x-3 group">
                                    <div class="w-2 h-2 bg-cyber-blue rounded-full"></div>
                                    <span class="group-hover:text-cyber-blue transition-colors">⚡ Prophetic analytics engine</span>
                                </div>
                                <div class="flex items-center space-x-3 group">
                                    <div class="w-2 h-2 bg-cyber-blue rounded-full"></div>
                                    <span class="group-hover:text-cyber-blue transition-colors">⚡ Industry covenant protocols</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-20">
            <div class="glass-card-premium p-8 max-w-2xl mx-auto">
                <h3 class="text-2xl font-orbitron font-bold text-cyber-cyan mb-4">Join Our Development Journey</h3>
                <p class="text-text-secondary mb-6">
                    Be part of the revolution. Your feedback and participation help shape the future of blockchain-based business verification.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ url_for('eden_mode.step1') }}"
                       class="glass-button-primary px-8 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                        🌟 Let's Begin
                    </a>
                    <a href="https://github.com/onnyx-labs" target="_blank"
                       class="glass-button px-8 py-3 rounded-xl font-orbitron font-semibold transition-all duration-300 hover:scale-105">
                        📚 View Documentation
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Final Call to Action -->
<div class="py-32 relative overflow-hidden">
    <!-- Background effects - Enhanced visibility -->
    <div class="absolute inset-0 hero-gradient"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <h2 class="text-5xl md:text-7xl font-orbitron font-bold mb-8">
            <span class="hologram-text">Reclaim Your Future</span>
        </h2>
        <p class="text-2xl md:text-3xl text-cyber-cyan font-orbitron font-medium mb-6">
            Break Free from Platform Dependency
        </p>
        <p class="text-xl text-gray-300 mb-16 max-w-4xl mx-auto leading-relaxed">
            Own your data, control your commerce, and build your reputation on the blockchain.
            Join the revolution of verified business operations where trust is transparent,
            rewards are fair, and your digital identity belongs to you.
        </p>

        <div class="flex flex-col sm:flex-row gap-8 justify-center items-center">
            <a href="{{ url_for('eden_mode.step1') }}"
               class="glass-button-primary px-12 py-6 rounded-xl text-xl font-orbitron font-bold transition-all duration-300 hover:scale-110 glow-effect">
                🌟 Let's Begin
            </a>
            <a href="{{ url_for('eden_mode.step4') }}"
               class="glass-button px-12 py-6 rounded-xl text-xl font-orbitron font-bold transition-all duration-300 hover:scale-110">
                🏢 Create Sela Business
            </a>
        </div>

        <!-- Trust indicators -->
        <div class="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div class="text-center trust-indicator-animated quantum-secure-indicator">
                <div class="w-16 h-16 bg-gradient-to-br from-cyber-cyan to-cyber-blue rounded-xl mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-cyan mb-2">Quantum-Secure</h3>
                <p class="text-gray-400">Military-grade cryptographic protection</p>
            </div>
            <div class="text-center trust-indicator-animated lightning-fast-indicator">
                <div class="w-16 h-16 bg-gradient-to-br from-cyber-purple to-cyber-blue rounded-xl mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-purple mb-2">Lightning Fast</h3>
                <p class="text-gray-400">Instant transaction verification</p>
            </div>
            <div class="text-center trust-indicator-animated global-network-indicator">
                <div class="w-16 h-16 bg-gradient-to-br from-cyber-blue to-green-400 rounded-xl mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-8 h-8 text-onyx-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-orbitron font-bold text-cyber-blue mb-2">Global Network</h3>
                <p class="text-gray-400">Worldwide validator infrastructure</p>
            </div>
        </div>
    </div>
</div>


</div> <!-- Close landing-content -->

<style>
/* Tribal Heritage Banner */
.tribal-banner {
    background: linear-gradient(135deg, rgba(154, 0, 255, 0.2), rgba(0, 255, 247, 0.1));
    border-bottom: 2px solid rgba(154, 0, 255, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 100;
    animation: banner-glow 4s ease-in-out infinite;
}

@keyframes banner-glow {
    0%, 100% {
        border-bottom-color: rgba(154, 0, 255, 0.3);
        box-shadow: 0 2px 20px rgba(154, 0, 255, 0.2);
    }
    50% {
        border-bottom-color: rgba(0, 255, 247, 0.5);
        box-shadow: 0 2px 30px rgba(0, 255, 247, 0.3);
    }
}

/* Enhanced Tribal Spinning N Animation */
.spinning-n {
    display: inline-block;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: spin 4s linear infinite;
    color: #9a00ff; /* Make visible by default with purple color */
    text-shadow: 0 0 10px #9a00ff; /* Add default glow */
}

/* Enhanced Tribal N's with more prominent styling */
.enhanced-tribal-n {
    background: linear-gradient(45deg, #9a00ff, #00fff7, #9a00ff);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: spin 4s linear infinite, tribal-glow 2s ease-in-out infinite alternate;
    filter: drop-shadow(0 0 15px #9a00ff);
}

.spinning-n:hover {
    animation-play-state: paused;
    color: #00fff7;
    text-shadow: 0 0 20px #00fff7, 0 0 40px #00fff7;
    transform: scale(1.1);
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes tribal-glow {
    0% {
        filter: drop-shadow(0 0 15px #9a00ff) drop-shadow(0 0 25px #9a00ff);
        background-position: 0% 50%;
    }
    50% {
        filter: drop-shadow(0 0 20px #00fff7) drop-shadow(0 0 35px #00fff7);
        background-position: 100% 50%;
    }
    100% {
        filter: drop-shadow(0 0 15px #9a00ff) drop-shadow(0 0 25px #9a00ff);
        background-position: 0% 50%;
    }
}

/* Tribal Heritage Indicator */
.tribal-heritage-indicator {
    background: linear-gradient(135deg, rgba(154, 0, 255, 0.1), rgba(0, 255, 247, 0.1));
    border: 1px solid rgba(154, 0, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    animation: heritage-pulse 3s ease-in-out infinite;
}

@keyframes heritage-pulse {
    0%, 100% {
        border-color: rgba(154, 0, 255, 0.3);
        box-shadow: 0 0 20px rgba(154, 0, 255, 0.2);
    }
    50% {
        border-color: rgba(0, 255, 247, 0.5);
        box-shadow: 0 0 30px rgba(0, 255, 247, 0.3);
    }
}

/* Enhanced Onyx Stone Tooltips */
.onyx-tooltip {
    position: absolute;
    background: linear-gradient(135deg, rgba(20, 30, 40, 0.98), rgba(30, 20, 40, 0.98));
    border: 3px solid #00fff7;
    border-radius: 20px;
    padding: 25px;
    min-width: 350px;
    box-shadow:
        0 0 30px rgba(0, 255, 247, 0.4),
        0 0 60px rgba(154, 0, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.4s ease;
    pointer-events: none;
}

.onyx-tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.tooltip-header {
    font-family: 'Orbitron', monospace;
    font-weight: bold;
    color: #00fff7;
    font-size: 20px;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 0 15px #00fff7, 0 0 25px #00fff7;
    background: linear-gradient(45deg, #00fff7, #9a00ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tooltip-names {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 15px;
}

.name-item {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.15), rgba(154, 0, 255, 0.1));
    padding: 12px 16px;
    border-radius: 12px;
    font-family: 'Orbitron', monospace;
    font-size: 16px;
    color: #ffffff;
    text-align: center;
    border: 2px solid rgba(0, 255, 247, 0.4);
    transition: all 0.3s ease;
    text-shadow: 0 0 8px rgba(0, 255, 247, 0.6);
    font-weight: 600;
}

.name-item:hover {
    background: linear-gradient(135deg, rgba(0, 255, 247, 0.3), rgba(154, 0, 255, 0.2));
    border-color: #00fff7;
    color: #00fff7;
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0, 255, 247, 0.5);
    text-shadow: 0 0 15px rgba(0, 255, 247, 0.8);
}

.tooltip-reference {
    font-family: 'Orbitron', monospace;
    font-size: 12px;
    color: #9a00ff;
    text-align: center;
    font-style: italic;
    opacity: 0.8;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const firstN = document.getElementById('first-n');
    const secondN = document.getElementById('second-n');
    const firstTooltip = document.getElementById('first-stone-tooltip');
    const secondTooltip = document.getElementById('second-stone-tooltip');

    let tooltipTimeout;

    function showTooltip(tooltip, element) {
        clearTimeout(tooltipTimeout);

        // Hide other tooltips
        document.querySelectorAll('.onyx-tooltip').forEach(t => {
            if (t !== tooltip) {
                t.classList.remove('show');
            }
        });

        // Position tooltip
        const rect = element.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();

        // Position below the element
        tooltip.style.left = (rect.left + rect.width / 2 - tooltipRect.width / 2) + 'px';
        tooltip.style.top = (rect.bottom + 20) + 'px';

        // Show tooltip
        tooltip.classList.add('show');
    }

    function hideTooltip(tooltip) {
        tooltipTimeout = setTimeout(() => {
            tooltip.classList.remove('show');
        }, 300);
    }

    // First N events
    firstN.addEventListener('mouseenter', () => showTooltip(firstTooltip, firstN));
    firstN.addEventListener('mouseleave', () => hideTooltip(firstTooltip));

    // Second N events
    secondN.addEventListener('mouseenter', () => showTooltip(secondTooltip, secondN));
    secondN.addEventListener('mouseleave', () => hideTooltip(secondTooltip));

    // Keep tooltip visible when hovering over it
    firstTooltip.addEventListener('mouseenter', () => clearTimeout(tooltipTimeout));
    firstTooltip.addEventListener('mouseleave', () => hideTooltip(firstTooltip));

    secondTooltip.addEventListener('mouseenter', () => clearTimeout(tooltipTimeout));
    secondTooltip.addEventListener('mouseleave', () => hideTooltip(secondTooltip));
});
</script>

{% endblock %}
