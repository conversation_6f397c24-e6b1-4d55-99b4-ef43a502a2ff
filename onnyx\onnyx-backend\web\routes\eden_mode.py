"""
Eden Mode Routes - Immersive Identity Onboarding Experience
Handles the spiritual awakening journey through biblical covenant registration
"""

from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from shared.db.db import db
from blockchain.tokenomics.biblical_tokenomics import BiblicalTokenomics
from shared.models.identity_verification import IdentityVerificationProposal
import hashlib
import secrets
import time
import json
from datetime import datetime
import logging
import time

logger = logging.getLogger(__name__)

# Secure access configuration
EDEN_MODE_ACCESS_CODES = {
    'covenant_awakening': 'secure_covenant_2024',  # Replace with secure hash
    'witness_path': 'witness_nations_2024'         # Replace with secure hash
}

def validate_eden_mode_session():
    """Validate that user has proper Eden Mode access"""
    if not session.get('eden_mode_validated'):
        return False

    # Check session timeout (30 minutes)
    timestamp = session.get('eden_mode_timestamp', 0)
    if int(time.time()) - timestamp > 1800:  # 30 minutes
        session.pop('eden_mode_validated', None)
        session.pop('eden_mode_path', None)
        session.pop('eden_mode_timestamp', None)
        return False

    return True

eden_mode_bp = Blueprint('eden_mode', __name__, url_prefix='/auth/eden-mode')

@eden_mode_bp.route('/')
def index():
    """Main Eden Mode entry point - redirects to step 1"""
    return redirect(url_for('eden_mode.step1'))

@eden_mode_bp.route('/step1')
def step1():
    """The Forgotten Legacy - Introduction to covenant awakening"""
    return render_template('auth/eden_mode_step1.html')

@eden_mode_bp.route('/validate-access', methods=['POST'])
def validate_access():
    """Secure server-side access validation for Eden Mode"""
    try:
        data = request.get_json()
        password = data.get('password', '')
        path_type = data.get('path_type', '')

        # Log access attempt
        logger.info(f"Eden Mode access attempt - Path: {path_type}, IP: {request.remote_addr}")

        # Rate limiting check (basic implementation)
        # In production, use Redis or similar for distributed rate limiting

        # Validate access code
        valid_access = False
        requires_gatekeeper = False

        if path_type == 'israelite' and password == EDEN_MODE_ACCESS_CODES['covenant_awakening']:
            valid_access = True
            requires_gatekeeper = True
        elif path_type == 'witness' and password == EDEN_MODE_ACCESS_CODES['witness_path']:
            valid_access = True
            requires_gatekeeper = False

        if valid_access:
            # Store access validation in session
            session['eden_mode_validated'] = True
            session['eden_mode_path'] = path_type
            session['eden_mode_timestamp'] = int(time.time())

            logger.info(f"Eden Mode access granted - Path: {path_type}, IP: {request.remote_addr}")

            return jsonify({
                'success': True,
                'requires_gatekeeper': requires_gatekeeper
            })
        else:
            # Log failed attempt
            logger.warning(f"Eden Mode access denied - Invalid password for path: {path_type}, IP: {request.remote_addr}")

            return jsonify({
                'success': False,
                'error': 'Invalid access code'
            }), 401

    except Exception as e:
        logger.error(f"Eden Mode access validation error: {e}")
        return jsonify({
            'success': False,
            'error': 'Validation failed'
        }), 500

# ISRAELITE COVENANT PATH
@eden_mode_bp.route('/israelite-step2')
def israelite_step2():
    """Israelite Heritage Selection - Choose your tribal lineage"""
    # Validate Eden Mode access
    if not validate_eden_mode_session():
        logger.warning(f"Unauthorized Eden Mode access attempt - israelite-step2, IP: {request.remote_addr}")
        return redirect(url_for('eden_mode.step1'))

    # Store covenant path in session
    session['covenant_path'] = 'israelite'
    session['requires_gatekeeper'] = True
    
    # Load the 12 tribes of Israel
    israelite_tribes = [
        {'tribe_name': 'Reuben', 'description': 'The firstborn, unstable as water', 'symbol': '💧', 'stone': 'Ruby'},
        {'tribe_name': 'Simeon', 'description': 'Scattered throughout Israel', 'symbol': '⚔️', 'stone': 'Topaz'},
        {'tribe_name': 'Levi', 'description': 'The priestly tribe, ministers of the sanctuary', 'symbol': '🕯️', 'stone': 'Emerald'},
        {'tribe_name': 'Judah', 'description': 'The royal tribe, lion of Judah', 'symbol': '🦁', 'stone': 'Carbuncle'},
        {'tribe_name': 'Dan', 'description': 'Judge of Israel, serpent by the way', 'symbol': '⚖️', 'stone': 'Sapphire'},
        {'tribe_name': 'Naphtali', 'description': 'A hind let loose, giving goodly words', 'symbol': '🦌', 'stone': 'Diamond'},
        {'tribe_name': 'Gad', 'description': 'A troop shall overcome him', 'symbol': '🛡️', 'stone': 'Ligure'},
        {'tribe_name': 'Asher', 'description': 'Out of Asher his bread shall be fat', 'symbol': '🌾', 'stone': 'Agate'},
        {'tribe_name': 'Issachar', 'description': 'A strong ass couching down between two burdens', 'symbol': '💪', 'stone': 'Amethyst'},
        {'tribe_name': 'Zebulun', 'description': 'Shall dwell at the haven of the sea', 'symbol': '🚢', 'stone': 'Beryl'},
        {'tribe_name': 'Joseph', 'description': 'A fruitful bough by a well', 'symbol': '🌿', 'stone': 'Onyx'},
        {'tribe_name': 'Benjamin', 'description': 'Shall ravin as a wolf', 'symbol': '🐺', 'stone': 'Jasper'}
    ]
    
    return render_template('auth/israelite_step2.html', israelite_tribes=israelite_tribes)

@eden_mode_bp.route('/israelite-step3')
def israelite_step3():
    """Israelite Heritage Verification - Upload proof and prepare for Gate Keeper review"""
    selected_tribe = request.args.get('tribe') or session.get('selected_tribe')
    if not selected_tribe:
        return redirect(url_for('eden_mode.israelite_step2'))
    
    session['selected_tribe'] = selected_tribe
    return render_template('auth/israelite_step3.html', selected_tribe=selected_tribe)

@eden_mode_bp.route('/israelite-step4')
def israelite_step4():
    """Gate Keeper Verification - Awaiting tribal council approval"""
    return render_template('auth/israelite_step4.html')

@eden_mode_bp.route('/israelite-step5')
def israelite_step5():
    """Covenant Inscription - Final step for verified Israelites"""
    return render_template('auth/israelite_step5.html')

# WITNESS NATION COVENANT PATH
@eden_mode_bp.route('/witness-step2')
def witness_step2():
    """Witness Nation Selection - Choose your heritage"""
    # Validate Eden Mode access
    if not validate_eden_mode_session():
        logger.warning(f"Unauthorized Eden Mode access attempt - witness-step2, IP: {request.remote_addr}")
        return redirect(url_for('eden_mode.step1'))

    # Store covenant path in session
    session['covenant_path'] = 'witness'
    session['requires_gatekeeper'] = False
    
    # Load witness nations organized by ancestral groups
    # For now, we'll use a simulated dataset since the DB query might not have the table
    try:
        biblical_nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, nation_type, description, flag_symbol,
                   ancestral_group, ancestral_description, historical_connection
            FROM biblical_nations
            WHERE nation_type = 'witness'
            ORDER BY ancestral_group, nation_name
        """)

        # Get unique ancestral groups
        ancestral_groups = db.query("""
            SELECT DISTINCT ancestral_group, COUNT(*) as nation_count
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group IS NOT NULL
            GROUP BY ancestral_group
            ORDER BY ancestral_group
        """)
    except Exception as e:
        # Fallback to empty data if table doesn't exist yet
        print(f"Database query failed: {e}")
        biblical_nations = []
        ancestral_groups = []

    return render_template('auth/witness_step2.html',
                         biblical_nations=biblical_nations,
                         ancestral_groups=ancestral_groups)

@eden_mode_bp.route('/witness-step3')
def witness_step3():
    """Witness Nation Heritage - Confirm your selection and role"""
    selected_nation = request.args.get('nation') or session.get('selected_nation')
    if not selected_nation:
        return redirect(url_for('eden_mode.witness_step2'))
    
    session['selected_nation'] = selected_nation
    return render_template('auth/witness_step3.html', selected_nation=selected_nation)

@eden_mode_bp.route('/witness-step4')
def witness_step4():
    """Covenant Inscription - Direct inscription for Witness Nations"""
    return render_template('auth/witness_step4.html')

# LEGACY ROUTES (for backward compatibility)
@eden_mode_bp.route('/step2')
def step2():
    """Legacy route - redirect based on covenant path"""
    covenant_path = session.get('covenant_path', 'witness')  # Default to witness
    if covenant_path == 'israelite':
        return redirect(url_for('eden_mode.israelite_step2'))
    else:
        return redirect(url_for('eden_mode.witness_step2'))

@eden_mode_bp.route('/step3')
def step3():
    """Legacy route - redirect based on covenant path"""
    covenant_path = session.get('covenant_path', 'witness')
    if covenant_path == 'israelite':
        return redirect(url_for('eden_mode.israelite_step3'))
    else:
        return redirect(url_for('eden_mode.witness_step3'))
    selected_tribe = request.args.get('tribe') or session.get('edenMode_selectedTribe')

    # Load tribal calling restrictions if a covenant tribe is selected
    tribal_callings = []
    if selected_tribe and selected_nation:
        # Check if this is a covenant tribe with specific callings
        tribal_callings = db.query("""
            SELECT calling_category, role_name, role_description, biblical_reference, is_exclusive
            FROM tribal_calling_restrictions
            WHERE tribe_code = ? OR tribe_name = ?
            ORDER BY calling_category, role_name
        """, [selected_nation.upper(), selected_tribe])

    return render_template('auth/eden_mode_step3.html',
                         selected_nation=selected_nation,
                         selected_tribe=selected_tribe,
                         tribal_callings=tribal_callings)

@eden_mode_bp.route('/step4')
def step4():
    """Eden Mode Activation - Business/Sela creation or joining"""
    return render_template('auth/eden_mode_step4.html')

@eden_mode_bp.route('/step5')
def step5():
    """Blockchain Inscription - Final identity creation"""
    return render_template('auth/eden_mode_step5.html')

@eden_mode_bp.route('/api/selas/available')
def get_available_selas():
    """Get list of available Sela businesses to join"""
    try:
        selas = db.query("""
            SELECT s.id, s.business_name, s.business_type, s.description, s.created_at,
                   COUNT(sr.id) as worker_count
            FROM selas s
            LEFT JOIN sela_relationships sr ON s.id = sr.sela_id AND sr.relationship_type = 'worker'
            WHERE s.status = 'active'
            GROUP BY s.id, s.business_name, s.business_type, s.description, s.created_at
            ORDER BY s.created_at DESC
        """)

        return jsonify(selas)
    except Exception as e:
        print(f"Error loading Selas: {e}")
        return jsonify([])

@eden_mode_bp.route('/create-identity', methods=['POST'])
def create_identity():
    """Create new covenant identity with Eden Mode data"""
    try:
        # Validate Eden Mode access
        if not validate_eden_mode_session():
            logger.warning(f"Unauthorized identity creation attempt, IP: {request.remote_addr}")
            return jsonify({'success': False, 'error': 'Unauthorized access'}), 401
        data = request.get_json()

        # Check for developer bypass (SECURED)
        is_developer_bypass = data.get('developerBypass', False)
        bypass_type = data.get('bypassType', '')

        # SECURITY: Developer bypass requires admin authentication
        if is_developer_bypass:
            # Log bypass attempt
            logger.warning(f"Developer bypass attempted, IP: {request.remote_addr}, Data: {data}")

            # For now, disable developer bypass in production
            # TODO: Implement proper admin authentication for bypass
            return jsonify({'success': False, 'error': 'Developer bypass disabled for security'}), 403

        # Comprehensive input validation
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        validation_errors = []

        # Validate required fields
        required_fields = ['fullName', 'email', 'selectedNation', 'laborCategory', 'selaChoice']
        for field in required_fields:
            if not data.get(field):
                validation_errors.append(f"Field '{field}' is required")

        # Validate field lengths and formats
        if data.get('fullName'):
            if len(data['fullName']) < 2 or len(data['fullName']) > 100:
                validation_errors.append("Full name must be between 2 and 100 characters")
            # Remove potentially dangerous characters
            import re
            if re.search(r'[<>"\']', data['fullName']):
                validation_errors.append("Full name contains invalid characters")

        if data.get('email'):
            email_pattern = r'^[^@]+@[^@]+\.[^@]+$'
            if not re.match(email_pattern, data['email']):
                validation_errors.append("Invalid email format")
            if len(data['email']) > 255:
                validation_errors.append("Email too long")

        # Validate nation code
        if data.get('selectedNation'):
            if len(data['selectedNation']) > 10:
                validation_errors.append("Nation code too long")

        # Return validation errors if any
        if validation_errors:
            logger.warning(f"Eden Mode validation errors: {validation_errors}, IP: {request.remote_addr}")
            return jsonify({'success': False, 'error': 'Validation failed', 'details': validation_errors}), 400

        # Generate cryptographic identity
        private_key = secrets.token_hex(32)
        public_key = hashlib.sha256(private_key.encode()).hexdigest()
        wallet_address = f"ONX{hashlib.sha256(public_key.encode()).hexdigest()[:34]}"

        # Generate unique identity ID
        identity_data = f"{data['fullName']}_{data['email']}_{int(time.time())}"
        identity_id = f"ONX{hashlib.sha256(identity_data.encode()).hexdigest()[:16]}"

        # Set verification level based on bypass
        if is_developer_bypass:
            verification_level = 9  # Developer bypass level
            status = 'active'
        else:
            verification_level = 1  # Regular Eden Mode
            status = 'active'

        # Determine if this is an Israelite covenant registration requiring Gate Keeper verification
        covenant_path = data.get('covenantPath', 'witness')
        requires_gate_keeper = covenant_path == 'israelite' and not is_developer_bypass

        # Set initial verification level based on covenant path
        if requires_gate_keeper:
            verification_level = 'Pending_Gate_Keeper_Verification'
            status = 'pending_verification'
        elif is_developer_bypass:
            verification_level = 'Tier_1_Verified'  # Developer bypass gets full verification
            status = 'active'
        else:
            verification_level = 'Tier_1_Verified'  # Witness nations get immediate verification
            status = 'active'

        # Create identity record (using production schema column names)
        identity_record = {
            'identity_id': identity_id,
            'name': data['fullName'],
            'email': data['email'],
            'public_key': public_key,
            'nation_id': data.get('selectedNation', 'UNKNOWN'),
            'nation_code': data.get('selectedNation', 'UNKNOWN'),
            'nation_name': data.get('selectedNationName', data.get('selectedNation', 'Unknown')),
            'metadata': json.dumps({
                'tribe_name': data.get('selectedTribe'),
                'labor_category': data['laborCategory'],
                'wallet_address': wallet_address,
                'eden_mode_completed': True,
                'registration_source': 'eden_mode',
                'developer_bypass': is_developer_bypass,
                'bypass_type': bypass_type,
                'covenant_path': covenant_path,
                'requires_gate_keeper_verification': requires_gate_keeper,
                'lineage_evidence': data.get('lineageEvidence', {}),
                'spiritual_testimony': data.get('spiritualTestimony', ''),
                'cultural_connections': data.get('culturalConnections', [])
            }),
            'status': status,
            'created_at': int(time.time()),
            'updated_at': int(time.time()),
            'covenant_accepted': True,
            'verification_level': verification_level,
            'role_class': covenant_path,
            'tribal_affiliation': data.get('selectedTribe', '').lower() if data.get('selectedTribe') else None,
            'vault_status': 'active',
            'etzem_score': 100,
            'protection_tier': 'Basic',
            'zeman_count': 0,
            'cipp_tier': 1,
            'full_name': data['fullName'],
            'tribe_name': data.get('selectedTribe'),
            'labor_category': data['laborCategory'],
            'eden_mode_completed': True,
            'sabbath_observer': True,
            'deeds_score': 50.0,
            'timezone': 'UTC'
        }

        db.insert('identities', identity_record)

        # Create Gate Keeper verification proposal for Israelite registrations
        verification_proposal = None
        if requires_gate_keeper:
            try:
                # Prepare applicant data for Gate Keeper review
                applicant_data = {
                    'name': data['fullName'],
                    'email': data['email'],
                    'tribe_name': data.get('selectedTribe'),
                    'lineage_evidence': data.get('lineageEvidence', {}),
                    'spiritual_testimony': data.get('spiritualTestimony', ''),
                    'cultural_connections': data.get('culturalConnections', []),
                    'family_records': data.get('familyRecords', []),
                    'dna_analysis': data.get('dnaAnalysis', {}),
                    'community_testimony': data.get('communityTestimony', ''),
                    'biblical_knowledge': data.get('biblicalKnowledge', {}),
                    'covenant_commitment': data.get('covenantCommitment', ''),
                    'registration_timestamp': int(time.time())
                }

                # Create the verification proposal
                verification_proposal = IdentityVerificationProposal.create_verification_proposal(
                    identity_id=identity_id,
                    tribe_code=data.get('selectedNation', 'UNKNOWN'),
                    applicant_data=applicant_data
                )

                logger.info(f"Created Gate Keeper verification proposal {verification_proposal.proposal_id} for {identity_id}")

            except Exception as e:
                logger.error(f"Error creating Gate Keeper verification proposal: {e}")
                # Continue with registration even if proposal creation fails

        # Handle Sela choice
        sela_id = None
        if data['selaChoice'] == 'create':
            # Create new Sela business
            sela_id = create_new_sela(identity_id, data)
        elif data['selaChoice'] == 'join' and data.get('selectedSela'):
            # Join existing Sela
            sela_id = join_existing_sela(identity_id, data['selectedSela'])
        elif data['selaChoice'] == 'community':
            # Community members don't need Sela association
            sela_id = None

        # Initialize biblical tokenomics
        initialize_biblical_tokenomics(identity_id, data)

        # Create blockchain transaction (simulated)
        block_number = create_covenant_transaction(identity_id, data)

        # Create session
        session['identity_id'] = identity_id
        session['wallet_address'] = wallet_address
        session['covenant_member'] = True

        # Prepare response based on verification status
        response_data = {
            'success': True,
            'identity_id': identity_id,
            'wallet_address': wallet_address,
            'sela_id': sela_id,
            'block_number': block_number,
            'covenant_path': covenant_path,
            'requires_gate_keeper_verification': requires_gate_keeper
        }

        if requires_gate_keeper and verification_proposal:
            response_data.update({
                'message': 'Identity registered - awaiting Gate Keeper verification',
                'verification_proposal_id': verification_proposal.proposal_id,
                'status': 'pending_gate_keeper_verification',
                'redirect_url': f'/governance/verification/{verification_proposal.proposal_id}',
                'next_steps': [
                    'Your Israelite identity claim has been submitted to the Council of 12 Gate Keepers',
                    'Gate Keepers have 30 days to review your evidence and vote',
                    'You can track the voting progress on the governance transparency page',
                    'Upon approval (7/12 votes), you will receive Tier 1 verification and Mikvah Token'
                ]
            })
        elif requires_gate_keeper and not verification_proposal:
            response_data.update({
                'message': 'Identity registered - Gate Keeper verification pending',
                'status': 'verification_system_error',
                'redirect_url': '/dashboard/',
                'warning': 'Gate Keeper verification proposal could not be created. Please contact support.'
            })
        else:
            response_data.update({
                'message': 'Covenant identity successfully created and verified',
                'status': 'verified',
                'redirect_url': '/dashboard/'
            })

        return jsonify(response_data)

    except Exception as e:
        print(f"Error creating identity: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

def create_new_sela(identity_id, data):
    """Create a new Sela business validator"""
    try:
        # Generate unique sela ID
        sela_data = f"{data['businessName']}_{identity_id}_{int(time.time())}"
        sela_id = f"SELA{hashlib.sha256(sela_data.encode()).hexdigest()[:16]}"

        # Create Sela record (using production schema column names)
        db.insert('selas', {
            'sela_id': sela_id,
            'identity_id': identity_id,
            'name': data['businessName'],  # Use 'name' instead of 'business_name'
            'category': data['businessType'],  # Use 'category' instead of 'business_type'
            'description': data['businessDescription'],
            'status': 'active',
            'created_at': int(time.time()),
            'updated_at': int(time.time()),
            'metadata': json.dumps({
                'is_validator': True,
                'accepting_members': True,
                'registration_source': 'eden_mode'
            })
        })

        # Note: In production schema, ownership is tracked via identity_id in selas table
        # No separate sela_relationships table needed

        # Initialize Sela in biblical tokenomics
        bt = BiblicalTokenomics(db)
        bt.register_sela_validator(sela_id, data['businessName'])

        return sela_id

    except Exception as e:
        print(f"Error creating Sela: {e}")
        raise

def join_existing_sela(identity_id, sela_id):
    """Join an existing Sela business"""
    try:
        # Verify Sela exists and is accepting members
        sela = db.query_one("""
            SELECT sela_id, name
            FROM selas
            WHERE sela_id = ? AND status = 'active'
        """, [sela_id])

        if not sela:
            raise Exception("Sela not found or inactive")

        # Note: Removed accepting_members check since column may not exist

        # Note: In production schema, joining a Sela would be tracked via labor_records table
        # For now, we'll just return the sela_id to indicate successful joining
        # The actual relationship will be established when the user creates labor records

        return sela_id

    except Exception as e:
        print(f"Error joining Sela: {e}")
        raise

def initialize_biblical_tokenomics(identity_id, data):
    """Initialize biblical tokenomics for new identity"""
    try:
        bt = BiblicalTokenomics(db)

        # Register identity in biblical tokenomics
        bt.register_covenant_member(
            identity_id=identity_id,
            nation_code=data['selectedNation'],
            labor_category=data['laborCategory']
        )

        # Grant initial Etzem tokens for covenant completion
        bt.award_etzem_tokens(
            identity_id=identity_id,
            amount=100,  # Welcome bonus
            reason="Eden Mode covenant completion",
            transaction_type="covenant_bonus"
        )

        # Initialize gleaning pool participation
        bt.initialize_gleaning_participation(identity_id)

        # Set up Sabbath enforcement
        bt.enable_sabbath_enforcement(identity_id)

    except Exception as e:
        print(f"Error initializing biblical tokenomics: {e}")
        raise

def create_covenant_transaction(identity_id, data):
    """Create blockchain transaction for covenant identity (simulated)"""
    try:
        # In a real implementation, this would create an actual blockchain transaction
        # For now, we'll simulate it by creating a record

        transaction_data = {
            'type': 'COVENANT_IDENTITY_CREATE',
            'identity_id': identity_id,
            'nation_code': data['selectedNation'],
            'labor_category': data['laborCategory'],
            'sela_choice': data['selaChoice'],
            'timestamp': int(time.time()),
            'covenant_hash': hashlib.sha256(f"{identity_id}{data['fullName']}{data['selectedNation']}".encode()).hexdigest()
        }

        # Simulate block creation
        try:
            block_count = db.query_one("SELECT COUNT(*) as count FROM blocks")
            block_number = (block_count['count'] if block_count else 0) + 1
        except:
            block_number = 1

        # Store transaction record in main transactions table (production schema)
        tx_data = f"{identity_id}{int(time.time())}"
        tx_id = f"TX{hashlib.sha256(tx_data.encode()).hexdigest()[:16]}"
        db.insert('transactions', {
            'tx_id': tx_id,
            'timestamp': int(time.time()),
            'op': 'COVENANT_IDENTITY_CREATE',
            'data': json.dumps(transaction_data),
            'sender': identity_id,
            'signature': hashlib.sha256(f"{tx_id}{identity_id}".encode()).hexdigest(),
            'status': 'confirmed',
            'created_at': int(time.time())
        })

        return block_number

    except Exception as e:
        print(f"Error creating covenant transaction: {e}")
        raise

@eden_mode_bp.route('/validate-nation', methods=['POST'])
def validate_nation():
    """Validate selected nation and return additional information"""
    try:
        data = request.get_json()
        nation_code = data.get('nation_code')

        if not nation_code:
            return jsonify({'success': False, 'error': 'Nation code required'}), 400

        nation = db.query_one("""
            SELECT nation_code, nation_name, tribe_name, nation_type, description, flag_symbol
            FROM biblical_nations
            WHERE nation_code = ?
        """, [nation_code])

        if not nation:
            return jsonify({'success': False, 'error': 'Nation not found'}), 404

        return jsonify({
            'success': True,
            'nation': nation
        })

    except Exception as e:
        print(f"Error validating nation: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/api/tribal-callings/<tribe_code>')
def get_tribal_callings(tribe_code):
    """Get available tribal callings for a specific tribe"""
    try:
        # Get tribal calling restrictions
        callings = db.query("""
            SELECT calling_category, role_name, role_description, biblical_reference, is_exclusive
            FROM tribal_calling_restrictions
            WHERE tribe_code = ? OR tribe_name = ?
            ORDER BY calling_category, role_name
        """, [tribe_code.upper(), tribe_code.title()])

        # Group by category
        calling_categories = {}
        for calling in callings:
            category = calling['calling_category']
            if category not in calling_categories:
                calling_categories[category] = []
            calling_categories[category].append(calling)

        return jsonify({
            'success': True,
            'tribe_code': tribe_code.upper(),
            'calling_categories': calling_categories,
            'total_callings': len(callings)
        })

    except Exception as e:
        print(f"Error getting tribal callings: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/api/validate-role-selection', methods=['POST'])
def validate_role_selection():
    """Validate if a user can select a specific role based on their tribal calling"""
    try:
        data = request.get_json()
        tribe_code = data.get('tribe_code', '').upper()
        selected_role = data.get('selected_role', '')

        if not tribe_code or not selected_role:
            return jsonify({'success': False, 'error': 'Tribe code and role required'}), 400

        # Check if this role is restricted to this tribe
        restriction = db.query_one("""
            SELECT role_name, role_description, biblical_reference, is_exclusive
            FROM tribal_calling_restrictions
            WHERE (tribe_code = ? OR tribe_name = ?) AND role_name = ?
        """, [tribe_code, tribe_code.title(), selected_role])

        if restriction:
            # Role is available to this tribe
            return jsonify({
                'success': True,
                'allowed': True,
                'role_info': restriction,
                'message': f'Role "{selected_role}" is available to the tribe of {tribe_code.title()}'
            })
        else:
            # Check if this role is exclusive to another tribe
            other_restriction = db.query_one("""
                SELECT tribe_name, biblical_reference
                FROM tribal_calling_restrictions
                WHERE role_name = ? AND is_exclusive = 1
            """, [selected_role])

            if other_restriction:
                return jsonify({
                    'success': True,
                    'allowed': False,
                    'message': f'Role "{selected_role}" is exclusively reserved for the tribe of {other_restriction["tribe_name"]}',
                    'biblical_reference': other_restriction['biblical_reference']
                })
            else:
                # Role is not restricted, available to all
                return jsonify({
                    'success': True,
                    'allowed': True,
                    'message': f'Role "{selected_role}" is available to all covenant members'
                })

    except Exception as e:
        print(f"Error validating role selection: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/api/ancestral-groups')
def get_ancestral_groups():
    """Get all ancestral groups for witness nations"""
    try:
        ancestral_groups = db.query("""
            SELECT ancestral_group, COUNT(*) as nation_count,
                   GROUP_CONCAT(nation_name, ', ') as nations
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group IS NOT NULL
            GROUP BY ancestral_group
            ORDER BY ancestral_group
        """)

        return jsonify({
            'success': True,
            'ancestral_groups': ancestral_groups,
            'total_groups': len(ancestral_groups)
        })

    except Exception as e:
        print(f"Error getting ancestral groups: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/api/nations-by-ancestry/<ancestral_group>')
def get_nations_by_ancestry(ancestral_group):
    """Get nations filtered by ancestral group"""
    try:
        nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, description, flag_symbol,
                   ancestral_group, ancestral_description, historical_connection
            FROM biblical_nations
            WHERE nation_type = 'witness' AND ancestral_group = ?
            ORDER BY nation_name
        """, [ancestral_group])

        return jsonify({
            'success': True,
            'ancestral_group': ancestral_group,
            'nations': nations,
            'nation_count': len(nations)
        })

    except Exception as e:
        print(f"Error getting nations by ancestry: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@eden_mode_bp.route('/check-email', methods=['POST'])
def check_email():
    """Check if email is already registered"""
    try:
        data = request.get_json()
        email = data.get('email')

        if not email:
            return jsonify({'success': False, 'error': 'Email required'}), 400

        existing = db.query_one("""
            SELECT id FROM identities WHERE email = ?
        """, [email])

        return jsonify({
            'success': True,
            'available': existing is None
        })

    except Exception as e:
        print(f"Error checking email: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

# Error handlers
@eden_mode_bp.errorhandler(404)
def not_found(error):
    return redirect(url_for('eden_mode.step1'))

@eden_mode_bp.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': 'Internal server error'}), 500
