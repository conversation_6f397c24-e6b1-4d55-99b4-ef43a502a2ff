{% extends "base.html" %}

{% block title %}Heritage Verification - ONNYX Eden Mode{% endblock %}
{% block meta_description %}Upload your heritage documentation and prepare for Gate Keeper tribal council verification to complete your Israelite covenant registration.{% endblock %}

{% block head %}
<style>
    .verification-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
    }
      .file-upload-area {
        border: 3px dashed #4F46E5;
        border-radius: 12px;
        padding: 3rem;
        text-align: center;
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(139, 92, 246, 0.05));
        transition: all 0.3s ease;
    }
    
    .file-upload-area:hover {
        border-color: #8B5CF6;
        background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(139, 92, 246, 0.1));
    }
    
    .file-upload-area.dragover {
        border-color: #10B981;
        background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(34, 197, 94, 0.1));
    }
    
    .verification-checklist {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .verification-item {
        display: flex;
        align-items: center;
        margin: 1rem 0;
        padding: 0.75rem;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.05);
    }
    
    .verification-item .icon {
        width: 2rem;
        height: 2rem;
        margin-right: 1rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #4F46E5, #8B5CF6);
    }
    
    .progress-indicator {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 2rem 0;
    }
    
    .progress-step {
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 0.5rem;
        font-weight: bold;
        color: white;
        position: relative;
    }
    
    .progress-step.completed {
        background: linear-gradient(135deg, #10B981, #34D399);
    }
    
    .progress-step.current {
        background: linear-gradient(135deg, #4F46E5, #8B5CF6);
        animation: pulse 2s infinite;
    }
    
    .progress-step.pending {
        background: rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.6);
    }
    
    .progress-connector {
        width: 3rem;
        height: 3px;
        background: rgba(255, 255, 255, 0.2);
        margin: 0 0.5rem;
    }
    
    .progress-connector.completed {
        background: linear-gradient(90deg, #10B981, #34D399);
    }
    
    .gatekeeper-info {
        background: linear-gradient(135deg, rgba(245, 158, 11, 0.1), rgba(251, 191, 36, 0.1));
        border: 1px solid rgba(245, 158, 11, 0.3);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 2rem 0;
    }
    
    .upload-preview {
        margin-top: 1rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 8px;
        display: none;
    }
    
    .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .file-item:last-child {
        border-bottom: none;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
    <!-- Animated background elements -->
    <div class="absolute inset-0">
        <div class="absolute top-10 left-10 w-32 h-32 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
        <div class="absolute top-40 right-20 w-48 h-48 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float-delayed"></div>
        <div class="absolute bottom-20 left-1/3 w-40 h-40 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float"></div>
    </div>

    <div class="container mx-auto px-4 py-8 relative z-10">        <!-- Progress Indicator -->
        <div class="progress-indicator">
            <div class="progress-step completed">1</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step completed">2</div>
            <div class="progress-connector completed"></div>
            <div class="progress-step current">3</div>
            <div class="progress-connector pending"></div>
            <div class="w-10 h-10 rounded-full bg-gradient-to-br from-purple-600 to-violet-600 flex items-center justify-center text-white font-bold text-lg shadow-lg shadow-purple-500/30">🗿</div>
            <div class="progress-connector pending"></div>
            <div class="progress-step pending">5</div>
        </div>

        <div class="verification-container">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-white mb-4">
                    <span class="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                        Heritage Verification
                    </span>
                </h1>
                <h2 class="text-xl text-purple-200 mb-2">Tribe of {{ selected_tribe }}</h2>
                <p class="text-gray-300 max-w-2xl mx-auto">
                    As an Israelite claiming heritage from the tribe of {{ selected_tribe }}, your documentation 
                    will be reviewed by our Gate Keeper tribal council for verification.
                </p>
            </div>

            <!-- Gate Keeper Information -->
            <div class="gatekeeper-info">
                <div class="flex items-center mb-3">
                    <span class="text-2xl mr-3">🛡️</span>
                    <h3 class="text-xl font-bold text-amber-100">Gate Keeper Verification Required</h3>
                </div>
                <p class="text-amber-200 mb-3">
                    All Israelite heritage claims require verification by our tribal council. This ensures the 
                    integrity of covenant membership and honors biblical lineage requirements.
                </p>
                <div class="text-sm text-amber-300">
                    <strong>What happens next:</strong> After uploading your documentation, a Gate Keeper 
                    will review your submission and contact you within 2-3 business days.
                </div>
            </div>

            <!-- Developer Bypass Section -->
            <div class="mt-6 mb-6">
                <button onclick="toggleDeveloperBypass()" 
                        class="text-sm text-gray-400 hover:text-cyber-cyan transition-colors duration-300 px-4 py-2 rounded border border-gray-600 hover:border-cyan-500"
                        style="opacity: 0.7;">
                    🔑 Developer Bypass Gate Keeper
                </button>
                <div id="developerBypass" class="hidden mt-4 max-w-md mx-auto">
                    <div class="bg-purple-600/10 border border-purple-500/20 rounded-lg p-6">
                        <h4 class="text-lg font-orbitron font-semibold text-purple-400 mb-4">
                            🔑 Developer Bypass
                        </h4>
                        <p class="text-sm text-gray-300 mb-4">
                            Enter the covenant phrase to bypass Gate Keeper verification and proceed directly to inscription
                        </p>
                        <input type="password"
                               id="bypassPassword"
                               placeholder="Enter covenant phrase (lowercase, no spaces)..."
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-300 mb-4">
                        <button onclick="validateBypass()" 
                                class="w-full px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg font-semibold transition-all duration-300 hover:scale-105">
                            🚀 Bypass Gate Keeper Review
                        </button>
                        <p class="text-xs text-gray-400 mt-2 text-center">
                            This will mark your identity as developer-verified and skip the review process
                        </p>
                    </div>
                </div>
            </div>

            <!-- Verification Checklist -->
            <div class="verification-checklist">
                <h3 class="text-xl font-bold text-white mb-4">Required Documentation</h3>                <div class="verification-item">
                    <div class="icon">📜</div>
                    <div>
                        <div class="font-semibold text-white">Genealogical Records</div>
                        <div class="text-sm text-gray-300">Family trees, birth certificates, ancestral documentation</div>
                    </div>
                </div>
                <div class="verification-item">
                    <div class="icon">⛪</div>
                    <div>
                        <div class="font-semibold text-white">Religious Documentation</div>
                        <div class="text-sm text-gray-300">Biblical community records, fellowship documentation</div>
                    </div>
                </div>
                <div class="verification-item">
                    <div class="icon">🧬</div>
                    <div>
                        <div class="font-semibold text-white">DNA Evidence (Optional)</div>
                        <div class="text-sm text-gray-300">Genetic testing results showing Middle Eastern ancestry</div>
                    </div>
                </div>
                <div class="verification-item">
                    <div class="icon">📋</div>
                    <div>
                        <div class="font-semibold text-white">Tribal Affiliation Statement</div>
                        <div class="text-sm text-gray-300">Written statement explaining your connection to {{ selected_tribe }}</div>
                    </div>
                </div>
            </div>

            <!-- File Upload Area -->
            <div class="file-upload-area" id="fileUploadArea">
                <div class="upload-icon text-6xl mb-4">📤</div>
                <h3 class="text-xl font-bold text-white mb-2">Upload Heritage Documentation</h3>
                <p class="text-gray-300 mb-4">
                    Drag and drop files here, or click to browse
                </p>
                <p class="text-sm text-gray-400 mb-4">
                    Accepted formats: PDF, JPG, PNG, DOCX (Max 10MB per file)
                </p>
                <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png,.docx" class="hidden">
                <button type="button" onclick="document.getElementById('fileInput').click()" 
                        class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                    Choose Files
                </button>
            </div>

            <!-- Upload Preview -->
            <div class="upload-preview" id="uploadPreview">
                <h4 class="font-bold text-white mb-3">Uploaded Files:</h4>
                <div id="fileList"></div>
            </div>

            <!-- Statement Textarea -->
            <div class="mt-6">
                <label for="tribalStatement" class="block text-sm font-medium text-white mb-2">
                    Tribal Affiliation Statement *
                </label>
                <textarea id="tribalStatement" rows="6" 
                         class="w-full p-4 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-purple-500 focus:ring-2 focus:ring-purple-500 focus:outline-none"
                         placeholder="Please explain your connection to the tribe of {{ selected_tribe }}. Include any family traditions, genealogical research, or spiritual calling that led you to this tribal affiliation..."></textarea>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between items-center mt-8">
                <a href="{{ url_for('eden_mode.israelite_step2') }}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300">
                    ← Back to Tribal Selection
                </a>
                
                <button id="submitVerification" disabled
                        class="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white font-semibold py-3 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 disabled:hover:scale-100">
                    Submit for Gate Keeper Review →
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadPreview = document.getElementById('uploadPreview');
    const fileList = document.getElementById('fileList');
    const tribalStatement = document.getElementById('tribalStatement');
    const submitButton = document.getElementById('submitVerification');
    let uploadedFiles = [];

    // File upload handling
    fileUploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', (e) => {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        handleFiles(e.dataTransfer.files);
    });

    fileUploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    fileInput.addEventListener('change', (e) => {
        handleFiles(e.target.files);
    });

    function handleFiles(files) {
        for (let file of files) {
            if (file.size > 10 * 1024 * 1024) {
                alert(`File ${file.name} is too large. Maximum size is 10MB.`);
                continue;
            }
            uploadedFiles.push(file);
        }
        updateFileList();
        checkFormCompletion();
    }

    function updateFileList() {
        if (uploadedFiles.length > 0) {
            uploadPreview.style.display = 'block';
            fileList.innerHTML = '';
            uploadedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span class="text-white">${file.name}</span>
                    <div>
                        <span class="text-sm text-gray-400">${(file.size / 1024 / 1024).toFixed(2)} MB</span>
                        <button onclick="removeFile(${index})" class="ml-3 text-red-400 hover:text-red-300">✕</button>
                    </div>
                `;
                fileList.appendChild(fileItem);
            });
        } else {
            uploadPreview.style.display = 'none';
        }
    }

    window.removeFile = function(index) {
        uploadedFiles.splice(index, 1);
        updateFileList();
        checkFormCompletion();
    };

    tribalStatement.addEventListener('input', checkFormCompletion);

    function checkFormCompletion() {
        const hasFiles = uploadedFiles.length > 0;
        const hasStatement = tribalStatement.value.trim().length > 50;
        submitButton.disabled = !(hasFiles && hasStatement);
    }

    submitButton.addEventListener('click', function() {
        if (uploadedFiles.length === 0) {
            alert('Please upload at least one document.');
            return;
        }
        
        if (tribalStatement.value.trim().length < 50) {
            alert('Please provide a detailed tribal affiliation statement (minimum 50 characters).');
            return;
        }

        // Store verification data in session
        sessionStorage.setItem('edenMode_verificationFiles', JSON.stringify(uploadedFiles.map(f => f.name)));
        sessionStorage.setItem('edenMode_tribalStatement', tribalStatement.value);
        sessionStorage.setItem('edenMode_verificationSubmitted', 'true');

        // Proceed to Gate Keeper step
        window.location.href = "{{ url_for('eden_mode.israelite_step4') }}";
    });    // Developer bypass functionality
    window.toggleDeveloperBypass = function() {
        const bypassSection = document.getElementById('developerBypass');
        bypassSection.classList.toggle('hidden');
        
        // Focus on password input if showing
        if (!bypassSection.classList.contains('hidden')) {
            setTimeout(() => {
                document.getElementById('bypassPassword').focus();
            }, 100);
        }
    };

    window.validateBypass = function() {
        const password = document.getElementById('bypassPassword').value.trim();
        const correctPassword = "israelunitedinchrist";

        if (password === correctPassword) {
            // Store bypass flags
            sessionStorage.setItem('developerBypass', 'true');
            sessionStorage.setItem('bypassType', 'developer');
            sessionStorage.setItem('bypassTimestamp', Date.now().toString());
            sessionStorage.setItem('skipGateKeeper', 'true');
            sessionStorage.setItem('verificationStatus', 'developer-bypassed');
            
            // Store that verification is "complete"
            sessionStorage.setItem('edenMode_verificationSubmitted', 'true');
            sessionStorage.setItem('edenMode_gatekeeperApproved', 'true');
            
            // Show success message
            const button = document.querySelector('button[onclick="validateBypass()"]');
            button.innerHTML = '✅ Bypass Activated';
            button.disabled = true;
            button.classList.remove('bg-gradient-to-r', 'from-purple-600', 'to-blue-600');
            button.classList.add('bg-green-600');
            
            // Hide upload form since it's no longer needed
            const uploadArea = document.getElementById('fileUploadArea');
            const checklist = document.querySelector('.verification-checklist');
            const statement = document.getElementById('tribalStatement').parentElement;
            
            if (uploadArea) uploadArea.style.opacity = '0.5';
            if (checklist) checklist.style.opacity = '0.5';
            if (statement) statement.style.opacity = '0.5';
            
            // Show success overlay
            setTimeout(() => {
                showBypassSuccess();
            }, 1000);
            
        } else {
            // Show error
            const input = document.getElementById('bypassPassword');
            input.style.borderColor = '#ef4444';
            input.style.boxShadow = '0 0 10px rgba(239, 68, 68, 0.3)';
            input.value = '';
            input.placeholder = 'Incorrect phrase - try again';
              setTimeout(() => {
                input.style.borderColor = '';
                input.style.boxShadow = '';
                input.placeholder = 'Enter covenant phrase (lowercase, no spaces)...';
            }, 2000);
        }
    };
    
    function showBypassSuccess() {
        // Create success overlay
        const overlay = document.createElement('div');
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.right = '0';
        overlay.style.bottom = '0';
        overlay.style.width = '100vw';
        overlay.style.height = '100vh';
        overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        overlay.style.backdropFilter = 'blur(8px)';
        overlay.style.zIndex = '99999';
        overlay.style.display = 'flex';        overlay.style.alignItems = 'flex-start';
        overlay.style.justifyContent = 'center';
        overlay.style.paddingTop = '10vh';
        overlay.style.padding = '20px';
        overlay.style.boxSizing = 'border-box';
        overlay.style.overflowY = 'auto';
        overlay.style.WebkitOverflowScrolling = 'touch';
        
        overlay.innerHTML = `
            <div style="
                background: rgba(17, 24, 39, 0.95);
                border: 1px solid rgba(34, 197, 94, 0.3);
                border-radius: 24px;
                padding: 32px;
                max-width: 400px;
                width: 100%;
                text-align: center;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
                max-height: 80vh;
                overflow-y: auto;
                margin: 20px auto;
            ">
                <div style="font-size: 3rem; margin-bottom: 24px;">🚀</div>
                <h3 style="font-size: 1.5rem; font-weight: bold; color: #22c55e; margin-bottom: 16px; margin-top: 0;">
                    Gate Keeper Bypass Activated
                </h3>
                <p style="color: #d1d5db; margin-bottom: 24px; line-height: 1.5; font-size: 0.9rem;">
                    Your identity has been marked as developer-verified. You are now bypassing the Gate Keeper verification process.
                </p>
                <div style="
                    background: rgba(34, 197, 94, 0.1);
                    border: 1px solid rgba(34, 197, 94, 0.2);
                    border-radius: 8px;
                    padding: 12px;
                    margin-bottom: 24px;
                ">
                    <p style="font-size: 0.75rem; font-weight: 600; color: #22c55e; margin: 0;">
                        ✅ Proceeding directly to covenant inscription for {{ selected_tribe }}
                    </p>
                </div>
                <button onclick="proceedWithBypass()" style="
                    width: 100%;
                    padding: 12px 24px;
                    background: linear-gradient(to right, #059669, #2563eb);
                    color: white;
                    border: none;
                    border-radius: 12px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-size: 1rem;
                ">
                    Continue to Inscription
                </button>
            </div>
        `;
        
        // Prevent body scroll when modal is open
        document.body.style.overflow = 'hidden';
        
        // Add click to close functionality
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                proceedWithBypass();
            }
        });
        
        // Add escape key to close
        const escapeHandler = function(e) {
            if (e.key === 'Escape') {
                document.removeEventListener('keydown', escapeHandler);
                proceedWithBypass();
            }
        };
        document.addEventListener('keydown', escapeHandler);
          document.body.appendChild(overlay);
    }
    
    function proceedWithBypass() {
        // Restore body scroll
        document.body.style.overflow = 'auto';
        
        // Go directly to step 5 (covenant inscription) bypassing step 4 (Gate Keeper waiting)
        window.location.href = '/auth/eden-mode/israelite-step5?bypass=developer&tribe={{ selected_tribe }}';
    }
});
</script>
{% endblock %}
