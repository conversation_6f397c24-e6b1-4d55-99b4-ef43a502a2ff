#!/usr/bin/env python3
"""
ONNYX P2P Network Deployment Script
Deploys the complete multi-node P2P network with tribal elders and community nodes
"""

import sys
import os
import asyncio
import json
import time
import logging
from typing import List, Dict

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from network.discovery.bootstrap import BootstrapNode
from network.nodes.tribal_elder_node import TribalElderNode, TribalElderConfig, create_tribal_elder_configs
from network.nodes.community_light_node import CommunityLightNode, CommunityNodeConfig
from shared.db.db import db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class NetworkDeployment:
    """Manages the deployment and coordination of the ONNYX P2P network"""
    
    def __init__(self):
        self.bootstrap_node = None
        self.tribal_elder_nodes: List[TribalElderNode] = []
        self.community_nodes: List[CommunityLightNode] = []
        self.running = False
        
        # Network configuration
        self.bootstrap_port = 8766
        self.bootstrap_address = "127.0.0.1"
        
    async def deploy_network(self) -> bool:
        """Deploy the complete ONNYX P2P network"""
        try:
            logger.info("🔥 DEPLOYING ONNYX P2P NETWORK")
            logger.info("=" * 50)
            
            # Phase 1: Deploy Bootstrap Node
            logger.info("📡 Phase 1: Deploying Bootstrap Node...")
            if not await self._deploy_bootstrap_node():
                logger.error("Failed to deploy bootstrap node")
                return False
            
            # Phase 2: Deploy Tribal Elder Nodes
            logger.info("👑 Phase 2: Deploying Tribal Elder Nodes...")
            if not await self._deploy_tribal_elder_nodes():
                logger.error("Failed to deploy tribal elder nodes")
                return False
            
            # Phase 3: Deploy Community Light Nodes
            logger.info("🏘️ Phase 3: Deploying Community Light Nodes...")
            if not await self._deploy_community_nodes():
                logger.error("Failed to deploy community nodes")
                return False
            
            # Phase 4: Verify Network Health
            logger.info("🔍 Phase 4: Verifying Network Health...")
            if not await self._verify_network_health():
                logger.error("Network health verification failed")
                return False
            
            self.running = True
            logger.info("🎉 ONNYX P2P NETWORK DEPLOYMENT COMPLETE!")
            return True
            
        except Exception as e:
            logger.error(f"Network deployment error: {e}")
            return False
    
    async def _deploy_bootstrap_node(self) -> bool:
        """Deploy the bootstrap node for peer discovery"""
        try:
            # Try multiple ports if the default is in use
            ports_to_try = [self.bootstrap_port, 8767, 8768, 8769, 8770]

            for port in ports_to_try:
                try:
                    self.bootstrap_node = BootstrapNode(
                        node_id="bootstrap_genesis",
                        port=port
                    )

                    await self.bootstrap_node.start()

                    # Wait for bootstrap node to initialize
                    await asyncio.sleep(2)

                    # Update the bootstrap port if we used a different one
                    self.bootstrap_port = port

                    logger.info(f"✅ Bootstrap node deployed on port {port}")
                    return True

                except Exception as port_error:
                    if "only one usage of each socket address" in str(port_error) or "Address already in use" in str(port_error):
                        logger.warning(f"Port {port} is in use, trying next port...")
                        continue
                    else:
                        # If it's not a port conflict, re-raise the error
                        raise port_error

            # If we get here, all ports failed
            logger.error(f"All bootstrap ports failed: {ports_to_try}")
            return False

        except Exception as e:
            logger.error(f"Bootstrap node deployment error: {e}")
            return False
    
    async def _deploy_tribal_elder_nodes(self) -> bool:
        """Deploy all 12 tribal elder validator nodes"""
        try:
            # Get tribal elder configurations
            elder_configs = create_tribal_elder_configs()
            
            # Deploy each tribal elder node
            for config in elder_configs:
                try:
                    elder_node = TribalElderNode(config)
                    
                    # Start the elder node
                    success = await elder_node.start()
                    
                    if success:
                        self.tribal_elder_nodes.append(elder_node)
                        logger.info(f"✅ Deployed {config.elder_name} ({config.tribal_code}) on port {config.port}")
                    else:
                        logger.error(f"❌ Failed to deploy {config.elder_name}")
                        return False
                    
                    # Brief pause between deployments
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    logger.error(f"Error deploying elder {config.tribal_code}: {e}")
                    return False
            
            logger.info(f"✅ Deployed {len(self.tribal_elder_nodes)}/12 tribal elder nodes")
            
            # Wait for elders to connect to each other
            await asyncio.sleep(5)
            
            return len(self.tribal_elder_nodes) == 12
            
        except Exception as e:
            logger.error(f"Tribal elder deployment error: {e}")
            return False
    
    async def _deploy_community_nodes(self) -> bool:
        """Deploy community light nodes for Tier 1+ members"""
        try:
            # Get Tier 1+ community members from database
            tier1_members = db.query("""
                SELECT identity_id, name, nation_code, verification_level, etzem_score
                FROM identities 
                WHERE verification_level >= 1 AND role_class = 'Community_Member'
                LIMIT 10
            """)
            
            if not tier1_members:
                logger.info("No Tier 1+ community members found, creating test members...")
                await self._create_test_community_members()
                tier1_members = db.query("""
                    SELECT identity_id, name, nation_code, verification_level, etzem_score
                    FROM identities 
                    WHERE verification_level >= 1 AND role_class = 'Community_Member'
                    LIMIT 5
                """)
            
            # Deploy community nodes
            port_start = 8800
            
            for i, member in enumerate(tier1_members):
                try:
                    config = CommunityNodeConfig(
                        identity_id=member['identity_id'],
                        name=member['name'],
                        tribal_code=member['nation_code'],
                        covenant_tier=member['verification_level'],
                        port=port_start + i,
                        bootstrap_nodes=[(self.bootstrap_address, self.bootstrap_port)],
                        etzem_score=member.get('etzem_score', 100)
                    )
                    
                    community_node = CommunityLightNode(config)
                    
                    # Start the community node
                    success = await community_node.start()
                    
                    if success:
                        self.community_nodes.append(community_node)
                        logger.info(f"✅ Deployed community node: {member['name']} ({member['nation_code']}, Tier {member['verification_level']})")
                    else:
                        logger.warning(f"⚠️ Failed to deploy community node for {member['name']}")
                    
                    # Brief pause between deployments
                    await asyncio.sleep(0.5)
                    
                except Exception as e:
                    logger.error(f"Error deploying community node for {member['name']}: {e}")
            
            logger.info(f"✅ Deployed {len(self.community_nodes)} community light nodes")
            return True
            
        except Exception as e:
            logger.error(f"Community node deployment error: {e}")
            return False
    
    async def _create_test_community_members(self):
        """Create test community members for network testing"""
        try:
            test_members = [
                ("Test Member Alpha", "<EMAIL>", "JU", 1),
                ("Test Member Beta", "<EMAIL>", "LE", 1),
                ("Test Member Gamma", "<EMAIL>", "EP", 2),
                ("Test Member Delta", "<EMAIL>", "BE", 1),
                ("Test Member Epsilon", "<EMAIL>", "SI", 2),
            ]
            
            for name, email, nation_code, tier in test_members:
                identity_id = f"test_{nation_code.lower()}_{int(time.time())}_{len(test_members)}"
                
                # Insert test member
                db.execute("""
                    INSERT INTO identities (
                        identity_id, name, email, public_key, nation_id, metadata,
                        status, created_at, updated_at, nation_of_origin, role_class,
                        etzem_score, verification_level, covenant_accepted, vault_status,
                        nation_code, nation_name
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    identity_id, name, email, f"pubkey_{identity_id}", nation_code,
                    json.dumps({"test_member": True}), "active", int(time.time()), int(time.time()),
                    nation_code, "Community_Member", 120, tier, True, "Active",
                    nation_code, {"JU": "Judah", "LE": "Levi", "EP": "Ephraim", "BE": "Benjamin", "SI": "Simeon"}[nation_code]
                ))
            
            logger.info(f"Created {len(test_members)} test community members")
            
        except Exception as e:
            logger.error(f"Test member creation error: {e}")
    
    async def _verify_network_health(self) -> bool:
        """Verify the health and connectivity of the deployed network"""
        try:
            logger.info("🔍 Verifying network health...")
            
            # Check bootstrap node
            if not self.bootstrap_node:
                logger.error("Bootstrap node not running")
                return False
            
            bootstrap_status = self.bootstrap_node.get_network_status()
            logger.info(f"📡 Bootstrap Node: {bootstrap_status['stats']['total_peers']} peers registered")
            
            # Check tribal elder nodes
            elder_health = []
            for elder in self.tribal_elder_nodes:
                status = elder.get_node_status()
                elder_health.append({
                    "tribal_code": status["tribal_code"],
                    "connected_peers": status["connected_peers"],
                    "tribal_elders_connected": status["tribal_elders_connected"],
                    "running": status["running"]
                })
            
            healthy_elders = len([e for e in elder_health if e["running"]])
            logger.info(f"👑 Tribal Elders: {healthy_elders}/12 healthy")
            
            # Check community nodes
            community_health = []
            for community in self.community_nodes:
                status = community.get_node_status()
                community_health.append({
                    "name": status["name"],
                    "connected_peers": status["connected_peers"],
                    "covenant_tier": status["covenant_tier"],
                    "running": status["running"]
                })
            
            healthy_community = len([c for c in community_health if c["running"]])
            logger.info(f"🏘️ Community Nodes: {healthy_community}/{len(self.community_nodes)} healthy")
            
            # Network health criteria
            min_healthy_elders = 8  # 2/3 of 12
            min_healthy_community = max(1, len(self.community_nodes) // 2)
            
            network_healthy = (
                healthy_elders >= min_healthy_elders and
                healthy_community >= min_healthy_community
            )
            
            if network_healthy:
                logger.info("✅ Network health verification PASSED")
                
                # Display network statistics
                await self._display_network_statistics()
                
                return True
            else:
                logger.error("❌ Network health verification FAILED")
                logger.error(f"   Required: {min_healthy_elders} elders, {min_healthy_community} community")
                logger.error(f"   Actual: {healthy_elders} elders, {healthy_community} community")
                return False
            
        except Exception as e:
            logger.error(f"Network health verification error: {e}")
            return False
    
    async def _display_network_statistics(self):
        """Display comprehensive network statistics"""
        try:
            logger.info("\n📊 NETWORK STATISTICS")
            logger.info("-" * 30)
            
            # Bootstrap statistics
            if self.bootstrap_node:
                bootstrap_stats = self.bootstrap_node.get_network_status()
                logger.info(f"📡 Bootstrap Node:")
                logger.info(f"   Total Registered Peers: {bootstrap_stats['stats']['total_peers']}")
                logger.info(f"   Tribal Elders: {bootstrap_stats['stats']['tribal_elders']}")
                logger.info(f"   Network Health: {bootstrap_stats['stats']['network_health']}")
            
            # Tribal elder statistics
            logger.info(f"\n👑 Tribal Elder Nodes ({len(self.tribal_elder_nodes)}):")
            total_elder_connections = 0
            for elder in self.tribal_elder_nodes:
                status = elder.get_node_status()
                logger.info(f"   {status['tribal_code']}: {status['connected_peers']} peers, Weight: {status['voting_weight']}")
                total_elder_connections += status['connected_peers']
            
            avg_elder_connections = total_elder_connections / max(len(self.tribal_elder_nodes), 1)
            logger.info(f"   Average Connections: {avg_elder_connections:.1f}")
            
            # Community node statistics
            logger.info(f"\n🏘️ Community Light Nodes ({len(self.community_nodes)}):")
            total_community_connections = 0
            tier_distribution = {}
            
            for community in self.community_nodes:
                status = community.get_node_status()
                tier = status['covenant_tier']
                tier_distribution[tier] = tier_distribution.get(tier, 0) + 1
                total_community_connections += status['connected_peers']
                logger.info(f"   {status['name']}: Tier {tier}, {status['connected_peers']} peers")
            
            if self.community_nodes:
                avg_community_connections = total_community_connections / len(self.community_nodes)
                logger.info(f"   Average Connections: {avg_community_connections:.1f}")
                logger.info(f"   Tier Distribution: {tier_distribution}")
            
            # Overall network health
            total_nodes = 1 + len(self.tribal_elder_nodes) + len(self.community_nodes)  # +1 for bootstrap
            logger.info(f"\n🌐 Overall Network:")
            logger.info(f"   Total Nodes: {total_nodes}")
            logger.info(f"   Network Type: Multi-node P2P Covenant Blockchain")
            logger.info(f"   Consensus: Proof-of-Covenant with Tribal Elder Validation")
            logger.info(f"   Biblical Compliance: Enforced at protocol level")
            
        except Exception as e:
            logger.error(f"Network statistics display error: {e}")
    
    async def test_network_consensus(self) -> bool:
        """Test network consensus by proposing a test voice scroll"""
        try:
            logger.info("\n🧪 TESTING NETWORK CONSENSUS")
            logger.info("-" * 30)
            
            if not self.tribal_elder_nodes:
                logger.error("No tribal elder nodes available for consensus testing")
                return False
            
            # Use the first tribal elder to propose a test voice scroll
            test_elder = self.tribal_elder_nodes[0]
            
            scroll_id = await test_elder.propose_voice_scroll(
                title="Network Deployment Test Scroll",
                content={
                    "purpose": "Test network consensus functionality",
                    "description": "This is a test voice scroll to verify that the multi-node consensus mechanism is working correctly.",
                    "test_parameters": {
                        "network_nodes": len(self.tribal_elder_nodes),
                        "deployment_time": int(time.time())
                    }
                },
                biblical_references=[
                    "Deuteronomy 19:15 - By the mouth of two or three witnesses every matter shall be established",
                    "Matthew 18:16 - Take with you one or two more, that by the mouth of two or three witnesses every word may be established"
                ]
            )
            
            if scroll_id:
                logger.info(f"✅ Test voice scroll proposed: {scroll_id}")
                
                # Wait for other elders to vote
                await asyncio.sleep(10)
                
                # Check consensus results
                consensus_stats = test_elder.consensus.get_consensus_stats()
                logger.info(f"📊 Consensus Statistics:")
                logger.info(f"   Active Voice Scrolls: {consensus_stats['active_voice_scrolls']}")
                logger.info(f"   Network Consensus Health: {consensus_stats['network_consensus_health']}")
                
                return True
            else:
                logger.error("❌ Failed to propose test voice scroll")
                return False
            
        except Exception as e:
            logger.error(f"Network consensus test error: {e}")
            return False
    
    async def shutdown_network(self):
        """Gracefully shutdown the entire network"""
        try:
            logger.info("🛑 Shutting down ONNYX P2P Network...")
            
            # Stop community nodes
            for community in self.community_nodes:
                await community.stop()
            
            # Stop tribal elder nodes
            for elder in self.tribal_elder_nodes:
                await elder.stop()
            
            # Stop bootstrap node
            if self.bootstrap_node:
                await self.bootstrap_node.stop()
            
            self.running = False
            logger.info("✅ Network shutdown complete")
            
        except Exception as e:
            logger.error(f"Network shutdown error: {e}")
    
    def get_network_status(self) -> dict:
        """Get comprehensive network status"""
        return {
            "running": self.running,
            "bootstrap_node": self.bootstrap_node is not None,
            "tribal_elder_nodes": len(self.tribal_elder_nodes),
            "community_nodes": len(self.community_nodes),
            "total_nodes": 1 + len(self.tribal_elder_nodes) + len(self.community_nodes),
            "deployment_time": int(time.time())
        }

async def main():
    """Main function to deploy and test the ONNYX P2P network"""
    logger.info("🌟 ONNYX P2P NETWORK DEPLOYMENT")
    logger.info("=" * 60)
    
    deployment = NetworkDeployment()
    
    try:
        # Deploy the network
        success = await deployment.deploy_network()
        
        if success:
            logger.info("\n🎉 DEPLOYMENT SUCCESSFUL!")
            
            # Test network consensus
            await deployment.test_network_consensus()
            
            # Keep network running for demonstration
            logger.info("\n⏳ Network running... Press Ctrl+C to shutdown")
            
            try:
                while deployment.running:
                    await asyncio.sleep(60)
                    
                    # Periodic health check
                    status = deployment.get_network_status()
                    logger.info(f"💓 Network heartbeat: {status['total_nodes']} nodes running")
                    
            except KeyboardInterrupt:
                logger.info("\n🛑 Shutdown requested by user")
        else:
            logger.error("\n❌ DEPLOYMENT FAILED!")
            
    except Exception as e:
        logger.error(f"Deployment error: {e}")
    finally:
        # Cleanup
        await deployment.shutdown_network()

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Deployment interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
