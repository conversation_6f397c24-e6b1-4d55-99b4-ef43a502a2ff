"""
Secure Authentication System for ONNYX Platform
Addresses critical security vulnerabilities in authentication and session management
"""

import hashlib
import secrets
import time
import json
import logging
from datetime import datetime, timedelta
from flask import session, request, current_app
from shared.db.db import db

logger = logging.getLogger(__name__)

class SecureAuth:
    """Secure authentication and session management"""
    
    # Session configuration
    SESSION_TIMEOUT = 3600  # 1 hour
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION = 900  # 15 minutes
    
    @staticmethod
    def hash_password(password: str, salt: str = None) -> tuple:
        """Hash password with salt using PBKDF2"""
        if salt is None:
            salt = secrets.token_hex(32)
        
        # Use PBKDF2 with SHA-256, 100,000 iterations
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000
        )
        
        return password_hash.hex(), salt
    
    @staticmethod
    def verify_password(password: str, stored_hash: str, salt: str) -> bool:
        """Verify password against stored hash"""
        try:
            computed_hash, _ = SecureAuth.hash_password(password, salt)
            return secrets.compare_digest(computed_hash, stored_hash)
        except Exception as e:
            logger.error(f"Password verification error: {e}")
            return False
    
    @staticmethod
    def generate_session_token() -> str:
        """Generate cryptographically secure session token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def create_secure_session(identity_id: str, user_data: dict) -> str:
        """Create secure session with token"""
        try:
            # Generate session token
            session_token = SecureAuth.generate_session_token()
            
            # Session data
            session_data = {
                'identity_id': identity_id,
                'user_data': user_data,
                'created_at': int(time.time()),
                'last_activity': int(time.time()),
                'ip_address': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', '')[:255],
                'csrf_token': secrets.token_urlsafe(32)
            }
            
            # Store session in database
            db.execute("""
                INSERT OR REPLACE INTO secure_sessions 
                (session_token, identity_id, session_data, created_at, last_activity, expires_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                session_token,
                identity_id,
                json.dumps(session_data),
                int(time.time()),
                int(time.time()),
                int(time.time()) + SecureAuth.SESSION_TIMEOUT
            ))
            
            # Set Flask session
            session['session_token'] = session_token
            session['identity_id'] = identity_id
            session['csrf_token'] = session_data['csrf_token']
            session.permanent = True
            
            # Log successful login
            SecureAuth.log_auth_event('LOGIN_SUCCESS', identity_id, {
                'ip_address': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', '')[:100]
            })
            
            return session_token
            
        except Exception as e:
            logger.error(f"Error creating secure session: {e}")
            raise
    
    @staticmethod
    def validate_session() -> dict:
        """Validate current session and return user data"""
        try:
            session_token = session.get('session_token')
            if not session_token:
                return None
            
            # Get session from database
            session_record = db.query_one("""
                SELECT * FROM secure_sessions 
                WHERE session_token = ? AND expires_at > ?
            """, (session_token, int(time.time())))
            
            if not session_record:
                SecureAuth.destroy_session()
                return None
            
            # Parse session data
            session_data = json.loads(session_record['session_data'])
            
            # Check for session timeout
            if int(time.time()) - session_data['last_activity'] > SecureAuth.SESSION_TIMEOUT:
                SecureAuth.destroy_session()
                SecureAuth.log_auth_event('SESSION_TIMEOUT', session_data['identity_id'])
                return None
            
            # Update last activity
            session_data['last_activity'] = int(time.time())
            db.execute("""
                UPDATE secure_sessions 
                SET session_data = ?, last_activity = ?
                WHERE session_token = ?
            """, (json.dumps(session_data), int(time.time()), session_token))
            
            return session_data
            
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            SecureAuth.destroy_session()
            return None
    
    @staticmethod
    def destroy_session():
        """Securely destroy current session"""
        try:
            session_token = session.get('session_token')
            if session_token:
                # Remove from database
                db.execute("DELETE FROM secure_sessions WHERE session_token = ?", (session_token,))
            
            # Clear Flask session
            session.clear()
            
        except Exception as e:
            logger.error(f"Error destroying session: {e}")
    
    @staticmethod
    def check_login_attempts(email: str) -> bool:
        """Check if user has exceeded login attempts"""
        try:
            cutoff_time = int(time.time()) - SecureAuth.LOCKOUT_DURATION
            
            attempts = db.query_one("""
                SELECT COUNT(*) as count FROM auth_attempts 
                WHERE email = ? AND timestamp > ? AND success = 0
            """, (email, cutoff_time))
            
            return (attempts['count'] if attempts else 0) < SecureAuth.MAX_LOGIN_ATTEMPTS
            
        except Exception as e:
            logger.error(f"Error checking login attempts: {e}")
            return True  # Allow login on error
    
    @staticmethod
    def record_login_attempt(email: str, success: bool, identity_id: str = None):
        """Record login attempt for rate limiting"""
        try:
            db.execute("""
                INSERT INTO auth_attempts 
                (email, identity_id, success, timestamp, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                email,
                identity_id,
                1 if success else 0,
                int(time.time()),
                request.remote_addr,
                request.headers.get('User-Agent', '')[:255]
            ))
            
        except Exception as e:
            logger.error(f"Error recording login attempt: {e}")
    
    @staticmethod
    def log_auth_event(event_type: str, identity_id: str, details: dict = None):
        """Log authentication events for security monitoring"""
        try:
            db.execute("""
                INSERT INTO security_audit_log 
                (event_type, user_id, details, timestamp, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (
                event_type,
                identity_id,
                json.dumps(details or {}),
                int(time.time()),
                request.remote_addr,
                request.headers.get('User-Agent', '')[:255]
            ))
            
        except Exception as e:
            logger.error(f"Error logging auth event: {e}")
    
    @staticmethod
    def cleanup_expired_sessions():
        """Clean up expired sessions (should be run periodically)"""
        try:
            current_time = int(time.time())
            
            # Delete expired sessions
            result = db.execute("""
                DELETE FROM secure_sessions WHERE expires_at < ?
            """, (current_time,))
            
            # Delete old auth attempts (older than 24 hours)
            cutoff_time = current_time - 86400
            db.execute("""
                DELETE FROM auth_attempts WHERE timestamp < ?
            """, (cutoff_time,))
            
            logger.info(f"Cleaned up expired sessions and old auth attempts")
            
        except Exception as e:
            logger.error(f"Error cleaning up sessions: {e}")
    
    @staticmethod
    def verify_csrf_token(token: str) -> bool:
        """Verify CSRF token"""
        try:
            session_csrf = session.get('csrf_token')
            return session_csrf and secrets.compare_digest(session_csrf, token)
        except Exception:
            return False
    
    @staticmethod
    def require_password_for_identity(identity_id: str):
        """Check if identity has a password set"""
        try:
            password_record = db.query_one("""
                SELECT password_hash FROM identity_passwords 
                WHERE identity_id = ?
            """, (identity_id,))
            
            return password_record is not None
            
        except Exception as e:
            logger.error(f"Error checking password requirement: {e}")
            return False
