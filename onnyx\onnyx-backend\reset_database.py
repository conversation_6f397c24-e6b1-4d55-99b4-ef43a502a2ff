#!/usr/bin/env python3
"""
Database Reset and Initialization Script for ONNYX Platform
Resets database and applies all security schema updates
"""

import os
import sys
import logging

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath('.'))

from shared.db.db import db

def reset_database():
    """Reset the entire database to a clean state"""
    print("🗄️ Resetting ONNYX database...")

    try:
        # Get database path
        db_path = db.db_path
        print(f"  Database path: {db_path}")

        # Close any existing connections
        try:
            db.close()
        except:
            pass

        # Delete the database file if it exists
        if os.path.exists(db_path):
            os.remove(db_path)
            print("  ✅ Database file deleted")
        else:
            print("  ℹ️  Database file doesn't exist")

        # Recreate the database directory
        os.makedirs(os.path.dirname(db_path), exist_ok=True)

        print("✅ Database reset complete")
        return True

    except Exception as e:
        print(f"❌ Database reset error: {e}")
        return False

def initialize_database():
    """Initialize database with all security schema updates"""
    print("🔧 Initializing database with security schema...")
    
    try:
        # Run the schema initialization
        from init_db import init_database
        init_database()
        
        print("✅ Database initialization complete")
        return True
        
    except Exception as e:
        print(f"❌ Database initialization error: {e}")
        return False

def create_genesis_identity():
    """Create Genesis Identity with admin privileges"""
    print("👑 Creating Genesis Identity...")
    
    try:
        from shared.models.identity import Identity
        from web.secure_auth import SecureAuth
        import secrets
        
        # Check if Genesis Identity already exists
        existing_genesis = db.query_one("SELECT * FROM identities WHERE email = ?", ('<EMAIL>',))
        
        if existing_genesis:
            print("  Genesis Identity already exists")
            return True
        
        # Create secure password for Genesis Identity
        genesis_password = "Genesis2024!Secure"  # Change this in production

        # Generate cryptographic keys for Genesis Identity
        from blockchain.wallet.wallet import Wallet
        wallet = Wallet()
        private_key, public_key = wallet.generate_keypair()

        # Create Genesis Identity with proper parameters
        genesis_identity = Identity.create(
            name='Genesis Administrator',
            public_key=public_key,
            email='<EMAIL>',
            nation_code='GENESIS',
            verification_level=3,
            metadata={
                'genesis_identity': True,
                'admin_privileges': True,
                'role': 'system_admin',
                'created_by': 'system_initialization',
                'private_key': private_key  # Store securely in production
            }
        )
        
        if genesis_identity:
            # Set secure password
            SecureAuth.set_password(genesis_identity.identity_id, genesis_password)
            
            print(f"  ✅ Genesis Identity created: {genesis_identity.identity_id}")
            print(f"  📧 Email: <EMAIL>")
            print(f"  🔑 Password: {genesis_password}")
            print("  ⚠️  CHANGE PASSWORD AFTER FIRST LOGIN!")
            
            return True
        else:
            print("  ❌ Failed to create Genesis Identity")
            return False
            
    except Exception as e:
        print(f"❌ Genesis Identity creation error: {e}")
        return False

def verify_security_tables():
    """Verify that all security tables are properly created"""
    print("🔍 Verifying security tables...")
    
    security_tables = [
        'secure_sessions',
        'identity_passwords', 
        'auth_attempts',
        'security_audit_log',
        'api_rate_limits',
        'gate_keepers',
        'voice_scroll_votes'
    ]
    
    try:
        for table in security_tables:
            result = db.query_one(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if result:
                print(f"  ✅ {table} table exists")
            else:
                print(f"  ❌ {table} table missing")
                return False
        
        print("✅ All security tables verified")
        return True
        
    except Exception as e:
        print(f"❌ Security table verification error: {e}")
        return False

def main():
    """Main database reset and initialization process"""
    print("🚀 ONNYX Database Reset and Initialization")
    print("=" * 50)
    
    # Step 1: Reset database
    if not reset_database():
        print("❌ Database reset failed. Aborting.")
        return False
    
    # Step 2: Initialize with security schema
    if not initialize_database():
        print("❌ Database initialization failed. Aborting.")
        return False
    
    # Step 3: Verify security tables
    if not verify_security_tables():
        print("❌ Security table verification failed. Aborting.")
        return False
    
    # Step 4: Create Genesis Identity
    if not create_genesis_identity():
        print("❌ Genesis Identity creation failed. Aborting.")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 DATABASE RESET AND INITIALIZATION COMPLETE!")
    print("=" * 50)
    print("✅ Database reset successfully")
    print("✅ Security schema applied")
    print("✅ Security tables verified")
    print("✅ Genesis Identity created")
    print("\n🔐 SECURITY STATUS: READY")
    print("🚀 System ready for secure operations")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
