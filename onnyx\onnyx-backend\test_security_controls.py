#!/usr/bin/env python3
"""
Security Controls Testing Script for ONNYX Platform
Tests all security fixes implemented during the comprehensive audit
"""

import requests
import json
import time
import sys

class SecurityTester:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, passed, details=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status}: {test_name}")
        if details:
            print(f"    {details}")
        
        self.test_results.append({
            'test': test_name,
            'passed': passed,
            'details': details
        })
    
    def test_api_authentication(self):
        """Test that API endpoints require authentication"""
        print("\n🔐 TESTING API AUTHENTICATION")
        print("-" * 40)
        
        # Test endpoints that should require authentication
        protected_endpoints = [
            "/api/stats",
            "/api/validators", 
            "/api/blocks/latest",
            "/api/search",
            "/api/tokenomics/gleaning-pool"
        ]
        
        for endpoint in protected_endpoints:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                # Should return 401 Unauthorized
                if response.status_code == 401:
                    self.log_test(f"API Auth: {endpoint}", True, "Correctly requires authentication")
                else:
                    self.log_test(f"API Auth: {endpoint}", False, f"Status: {response.status_code} (should be 401)")
                    
            except Exception as e:
                self.log_test(f"API Auth: {endpoint}", False, f"Error: {e}")
    
    def test_csrf_protection(self):
        """Test CSRF protection on forms"""
        print("\n🛡️ TESTING CSRF PROTECTION")
        print("-" * 40)
        
        # Test POST endpoints that should require CSRF tokens
        csrf_endpoints = [
            ("/api/validate/email", {"email": "<EMAIL>"}),
            ("/api/validate/sela-name", {"name": "TestSela"})
        ]
        
        for endpoint, data in csrf_endpoints:
            try:
                response = self.session.post(f"{self.base_url}{endpoint}", json=data)
                
                # Should return 403 Forbidden due to missing CSRF token
                if response.status_code == 403:
                    self.log_test(f"CSRF: {endpoint}", True, "Correctly requires CSRF token")
                else:
                    self.log_test(f"CSRF: {endpoint}", False, f"Status: {response.status_code} (should be 403)")
                    
            except Exception as e:
                self.log_test(f"CSRF: {endpoint}", False, f"Error: {e}")
    
    def test_input_validation(self):
        """Test input validation"""
        print("\n📝 TESTING INPUT VALIDATION")
        print("-" * 40)
        
        # Test malicious input patterns
        malicious_inputs = [
            "<script>alert('xss')</script>",
            "'; DROP TABLE users; --",
            "../../../etc/passwd",
            "javascript:alert('xss')"
        ]
        
        for malicious_input in malicious_inputs:
            try:
                # Test email validation endpoint
                response = self.session.post(f"{self.base_url}/api/validate/email", 
                                           json={"email": malicious_input})
                
                # Should reject malicious input
                if response.status_code in [400, 403]:
                    self.log_test(f"Input Validation: {malicious_input[:20]}...", True, "Malicious input rejected")
                else:
                    self.log_test(f"Input Validation: {malicious_input[:20]}...", False, f"Status: {response.status_code}")
                    
            except Exception as e:
                self.log_test(f"Input Validation: {malicious_input[:20]}...", False, f"Error: {e}")
    
    def test_security_headers(self):
        """Test security headers"""
        print("\n🔒 TESTING SECURITY HEADERS")
        print("-" * 40)
        
        try:
            response = self.session.get(f"{self.base_url}/")
            headers = response.headers
            
            # Check for security headers
            security_headers = [
                "Content-Security-Policy",
                "X-Content-Type-Options", 
                "X-Frame-Options",
                "X-XSS-Protection",
                "Strict-Transport-Security"
            ]
            
            for header in security_headers:
                if header in headers:
                    self.log_test(f"Security Header: {header}", True, f"Value: {headers[header][:50]}...")
                else:
                    self.log_test(f"Security Header: {header}", False, "Header missing")
                    
        except Exception as e:
            self.log_test("Security Headers", False, f"Error: {e}")
    
    def test_eden_mode_security(self):
        """Test Eden Mode security fixes"""
        print("\n🌟 TESTING EDEN MODE SECURITY")
        print("-" * 40)
        
        try:
            # Test that Eden Mode step 1 loads
            response = self.session.get(f"{self.base_url}/auth/eden-mode/step1")
            
            if response.status_code == 200:
                # Check that hardcoded passwords are not in the response
                content = response.text.lower()
                
                if "covenant123" in content:
                    self.log_test("Eden Mode: Hardcoded Password", False, "Hardcoded password still present")
                else:
                    self.log_test("Eden Mode: Hardcoded Password", True, "No hardcoded passwords found")
                
                # Check for server-side validation
                if "validate-access" in content:
                    self.log_test("Eden Mode: Server Validation", True, "Server-side validation implemented")
                else:
                    self.log_test("Eden Mode: Server Validation", False, "Server-side validation missing")
            else:
                self.log_test("Eden Mode: Access", False, f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_test("Eden Mode Security", False, f"Error: {e}")
    
    def test_rate_limiting(self):
        """Test API rate limiting"""
        print("\n⏱️ TESTING RATE LIMITING")
        print("-" * 40)
        
        try:
            # Make multiple rapid requests to test rate limiting
            endpoint = f"{self.base_url}/api/validate/email"
            
            rate_limited = False
            for i in range(15):  # Try 15 requests rapidly
                response = self.session.post(endpoint, json={"email": f"test{i}@example.com"})
                
                if response.status_code == 429:  # Too Many Requests
                    rate_limited = True
                    break
                    
                time.sleep(0.1)  # Small delay
            
            if rate_limited:
                self.log_test("Rate Limiting", True, "Rate limiting active")
            else:
                self.log_test("Rate Limiting", False, "No rate limiting detected")
                
        except Exception as e:
            self.log_test("Rate Limiting", False, f"Error: {e}")
    
    def test_admin_access_control(self):
        """Test admin access controls"""
        print("\n👑 TESTING ADMIN ACCESS CONTROLS")
        print("-" * 40)
        
        try:
            # Test admin-only endpoints
            admin_endpoints = [
                "/onboarding/",
                "/admin/dashboard"
            ]
            
            for endpoint in admin_endpoints:
                response = self.session.get(f"{self.base_url}{endpoint}")
                
                # Should redirect to login or return 401/403
                if response.status_code in [302, 401, 403]:
                    self.log_test(f"Admin Access: {endpoint}", True, f"Access restricted (Status: {response.status_code})")
                else:
                    self.log_test(f"Admin Access: {endpoint}", False, f"Status: {response.status_code}")
                    
        except Exception as e:
            self.log_test("Admin Access Control", False, f"Error: {e}")
    
    def generate_report(self):
        """Generate final security test report"""
        print("\n" + "=" * 60)
        print("🔒 SECURITY CONTROLS TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['passed'])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📊 SUMMARY:")
        print(f"  • Total Tests: {total_tests}")
        print(f"  • ✅ Passed: {passed_tests}")
        print(f"  • ❌ Failed: {failed_tests}")
        print(f"  • Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            print("-" * 30)
            for i, result in enumerate([r for r in self.test_results if not r['passed']], 1):
                print(f"  {i:2d}. {result['test']}")
                if result['details']:
                    print(f"      {result['details']}")
        
        print(f"\n🎯 SECURITY STATUS:")
        if failed_tests == 0:
            print("  🟢 ALL SECURITY CONTROLS WORKING")
        elif failed_tests <= 3:
            print("  🟡 MINOR SECURITY ISSUES DETECTED")
        else:
            print("  🔴 MAJOR SECURITY ISSUES DETECTED")
        
        return failed_tests == 0

def main():
    """Run comprehensive security testing"""
    print("🚀 ONNYX SECURITY CONTROLS TESTING")
    print("=" * 60)
    
    tester = SecurityTester()
    
    # Run all security tests
    tester.test_api_authentication()
    tester.test_csrf_protection()
    tester.test_input_validation()
    tester.test_security_headers()
    tester.test_eden_mode_security()
    tester.test_rate_limiting()
    tester.test_admin_access_control()
    
    # Generate final report
    all_passed = tester.generate_report()
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
