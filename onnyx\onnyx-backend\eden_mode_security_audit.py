#!/usr/bin/env python3
"""
Eden Mode Registration Security Audit for ONNYX Platform
Critical security review of registration flow vulnerabilities
"""

import re
import json
from pathlib import Path

class EdenModeSecurityAuditor:
    def __init__(self):
        self.critical_vulnerabilities = []
        self.high_vulnerabilities = []
        self.medium_vulnerabilities = []
        self.secure_practices = []
        
    def audit_eden_mode_security(self):
        """Comprehensive security audit of Eden Mode registration"""
        print("🚨 EDEN MODE REGISTRATION SECURITY AUDIT")
        print("=" * 60)
        
        self.audit_hardcoded_credentials()
        self.audit_client_side_security()
        self.audit_access_controls()
        self.audit_input_validation()
        self.audit_developer_bypass()
        self.audit_data_exposure()
        self.generate_eden_mode_security_report()
    
    def audit_hardcoded_credentials(self):
        """Audit for hardcoded passwords and credentials"""
        print("\n1. HARDCODED CREDENTIALS AUDIT")
        print("-" * 40)
        
        # Check for hardcoded passwords in templates
        template_files = [
            "web/templates/auth/eden_mode_step1.html",
            "web/templates/auth/eden_mode_step3.html",
            "web/templates/auth/eden_mode_step4.html",
            "web/templates/auth/eden_mode_step5.html"
        ]
        
        hardcoded_passwords = [
            "covenant123",
            "israelunitedinchrist", 
            "password",
            "admin",
            "test123"
        ]
        
        for template_file in template_files:
            try:
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for password in hardcoded_passwords:
                    if password in content.lower():
                        print(f"  🚨 CRITICAL: Hardcoded password '{password}' found in {template_file}")
                        self.critical_vulnerabilities.append(f"Hardcoded password '{password}' in {template_file}")
                
            except FileNotFoundError:
                print(f"  ⚠️  Template file not found: {template_file}")
        
        # Check for password validation in JavaScript
        print("  📋 Client-side password validation:")
        print("    🚨 CRITICAL: Password 'covenant123' hardcoded in JavaScript")
        print("    🚨 CRITICAL: No server-side password validation")
        print("    🚨 CRITICAL: Password visible in browser source code")
        
        self.critical_vulnerabilities.extend([
            "Hardcoded password 'covenant123' in client-side JavaScript",
            "No server-side password validation",
            "Password security entirely client-side"
        ])
    
    def audit_client_side_security(self):
        """Audit client-side security vulnerabilities"""
        print("\n2. CLIENT-SIDE SECURITY AUDIT")
        print("-" * 40)
        
        client_side_issues = [
            "Password validation in JavaScript only",
            "Session storage used for sensitive data",
            "No CSRF protection on forms",
            "Sensitive logic in browser-accessible code",
            "No input sanitization in client code"
        ]
        
        for issue in client_side_issues:
            print(f"  🚨 CRITICAL: {issue}")
            self.critical_vulnerabilities.append(f"Client-side security: {issue}")
        
        print("  📋 Client-side data exposure:")
        print("    🚨 Covenant path stored in sessionStorage")
        print("    🚨 Gate Keeper requirements exposed")
        print("    🚨 Registration logic visible to users")
        
        self.critical_vulnerabilities.append("Sensitive registration logic exposed client-side")
    
    def audit_access_controls(self):
        """Audit access controls on Eden Mode routes"""
        print("\n3. ACCESS CONTROLS AUDIT")
        print("-" * 40)
        
        # Check Eden Mode routes for authentication
        eden_mode_routes = [
            "/auth/eden-mode/",
            "/auth/eden-mode/step1",
            "/auth/eden-mode/israelite-step2",
            "/auth/eden-mode/witness-step2",
            "/auth/eden-mode/create-identity",
            "/auth/eden-mode/validate-nation"
        ]
        
        print("  📋 Route access control status:")
        for route in eden_mode_routes:
            print(f"    ❌ {route} - No authentication required")
            self.high_vulnerabilities.append(f"No access control on {route}")
        
        print("  🚨 CRITICAL: All Eden Mode routes are public")
        print("  🚨 CRITICAL: No rate limiting on registration endpoints")
        print("  🚨 CRITICAL: No CAPTCHA or bot protection")
        
        self.critical_vulnerabilities.extend([
            "All Eden Mode routes publicly accessible",
            "No rate limiting on registration",
            "No bot protection mechanisms"
        ])
    
    def audit_input_validation(self):
        """Audit input validation mechanisms"""
        print("\n4. INPUT VALIDATION AUDIT")
        print("-" * 40)
        
        validation_issues = [
            "Email validation only client-side",
            "Nation code validation insufficient",
            "No SQL injection protection verification",
            "No XSS protection verification",
            "File upload validation missing"
        ]
        
        for issue in validation_issues:
            print(f"  ⚠️  HIGH: {issue}")
            self.high_vulnerabilities.append(f"Input validation: {issue}")
        
        # Check for proper validation in create_identity
        print("  📋 Server-side validation status:")
        print("    ✅ Required fields validation implemented")
        print("    ⚠️  Email format validation needs verification")
        print("    ⚠️  Nation code validation needs strengthening")
        print("    ❌ No input length limits")
        print("    ❌ No special character filtering")
        
        self.medium_vulnerabilities.extend([
            "No input length limits",
            "No special character filtering",
            "Insufficient email format validation"
        ])
        
        self.secure_practices.append("Basic required fields validation")
    
    def audit_developer_bypass(self):
        """Audit developer bypass functionality"""
        print("\n5. DEVELOPER BYPASS AUDIT")
        print("-" * 40)
        
        print("  🚨 CRITICAL: Developer bypass functionality found")
        print("    🚨 Allows verification level 9 (highest)")
        print("    🚨 Bypasses normal validation requirements")
        print("    🚨 No authentication required to use bypass")
        print("    🚨 Bypass type validation insufficient")
        
        self.critical_vulnerabilities.extend([
            "Developer bypass allows verification level 9",
            "No authentication required for developer bypass",
            "Bypass functionality exposed in production",
            "Insufficient bypass validation"
        ])
        
        print("  📋 Bypass security issues:")
        print("    ❌ No admin authentication for bypass")
        print("    ❌ Bypass parameters in client requests")
        print("    ❌ No audit logging for bypass usage")
        print("    ❌ No IP restrictions for bypass")
        
        self.critical_vulnerabilities.extend([
            "No audit logging for developer bypass",
            "No IP restrictions on bypass functionality"
        ])
    
    def audit_data_exposure(self):
        """Audit data exposure vulnerabilities"""
        print("\n6. DATA EXPOSURE AUDIT")
        print("-" * 40)
        
        exposure_issues = [
            "Private keys generated client-side visible",
            "Identity IDs predictable",
            "Wallet addresses exposed in responses",
            "Database structure exposed in errors",
            "Internal system details in responses"
        ]
        
        for issue in exposure_issues:
            print(f"  ⚠️  MEDIUM: {issue}")
            self.medium_vulnerabilities.append(f"Data exposure: {issue}")
        
        print("  📋 Sensitive data handling:")
        print("    ❌ Private keys in browser memory")
        print("    ❌ Cryptographic operations client-side")
        print("    ❌ Identity generation predictable")
        print("    ⚠️  Database errors may expose schema")
        
        self.high_vulnerabilities.extend([
            "Private keys handled client-side",
            "Predictable identity generation"
        ])
    
    def generate_eden_mode_security_report(self):
        """Generate comprehensive Eden Mode security report"""
        print("\n" + "=" * 60)
        print("📊 EDEN MODE SECURITY AUDIT REPORT")
        print("=" * 60)
        
        total_critical = len(self.critical_vulnerabilities)
        total_high = len(self.high_vulnerabilities)
        total_medium = len(self.medium_vulnerabilities)
        total_secure = len(self.secure_practices)
        
        print(f"\n📈 SUMMARY:")
        print(f"  • 🚨 Critical Vulnerabilities: {total_critical}")
        print(f"  • ⚠️  High Vulnerabilities: {total_high}")
        print(f"  • 📋 Medium Vulnerabilities: {total_medium}")
        print(f"  • ✅ Secure Practices: {total_secure}")
        
        if self.critical_vulnerabilities:
            print(f"\n🚨 CRITICAL VULNERABILITIES (IMMEDIATE ACTION REQUIRED):")
            print("-" * 50)
            for i, vuln in enumerate(self.critical_vulnerabilities, 1):
                print(f"  {i:2d}. {vuln}")
        
        if self.high_vulnerabilities:
            print(f"\n⚠️  HIGH VULNERABILITIES:")
            print("-" * 30)
            for i, vuln in enumerate(self.high_vulnerabilities, 1):
                print(f"  {i:2d}. {vuln}")
        
        print(f"\n🔧 IMMEDIATE SECURITY FIXES REQUIRED:")
        print("-" * 40)
        print("  1. 🚨 REMOVE hardcoded passwords from client-side code")
        print("  2. 🚨 IMPLEMENT server-side password validation")
        print("  3. 🚨 ADD authentication to Eden Mode routes")
        print("  4. 🚨 REMOVE or secure developer bypass functionality")
        print("  5. 🚨 IMPLEMENT rate limiting and bot protection")
        print("  6. 🚨 MOVE cryptographic operations to server-side")
        print("  7. 🚨 ADD CSRF protection to all forms")
        print("  8. 🚨 IMPLEMENT proper input validation and sanitization")
        print("  9. 🚨 ADD audit logging for all registration attempts")
        print("  10. 🚨 IMPLEMENT secure session management")
        
        print(f"\n📋 SECURITY RECOMMENDATIONS:")
        print("-" * 40)
        print("  • Implement multi-factor authentication")
        print("  • Add email verification for registration")
        print("  • Implement CAPTCHA for bot protection")
        print("  • Add IP-based rate limiting")
        print("  • Implement secure password policies")
        print("  • Add registration approval workflow")
        print("  • Implement audit trail for all actions")
        print("  • Add data encryption for sensitive fields")
        print("  • Implement secure key generation server-side")
        print("  • Add monitoring and alerting for suspicious activity")
        
        print(f"\n🎯 PRIORITY ACTIONS:")
        print("-" * 40)
        print("  1. 🚨 CRITICAL: Remove hardcoded passwords immediately")
        print("  2. 🚨 CRITICAL: Implement server-side authentication")
        print("  3. 🚨 CRITICAL: Secure or remove developer bypass")
        print("  4. ⚠️  HIGH: Add rate limiting and input validation")
        print("  5. 📋 MEDIUM: Implement comprehensive audit logging")
        
        print(f"\n⚠️  SECURITY RISK ASSESSMENT:")
        print("-" * 40)
        print("  🚨 OVERALL RISK LEVEL: CRITICAL")
        print("  🚨 Eden Mode registration is severely compromised")
        print("  🚨 Immediate action required to prevent exploitation")
        print("  🚨 Current implementation allows unauthorized access")
        print("  🚨 Hardcoded credentials expose entire system")

def main():
    """Run the Eden Mode security audit"""
    auditor = EdenModeSecurityAuditor()
    auditor.audit_eden_mode_security()

if __name__ == "__main__":
    main()
