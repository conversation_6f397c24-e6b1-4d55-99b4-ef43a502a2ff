"""
Authentication Routes

Handles identity registration, login, and authentication.
"""

import os
import sys
import json
import time
import logging
from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for, flash

# Add the parent directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from shared.models.identity import Identity
from blockchain.wallet.wallet import Wallet
from shared.db.db import db
from web.secure_auth import SecureAuth

logger = logging.getLogger("onnyx.web.auth")

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register/identity')
def register_identity():
    """Identity registration page - redirects to Eden Mode immersive experience."""
    # Redirect to Eden Mode for the complete immersive experience
    return redirect(url_for('eden_mode.step1'))

@auth_bp.route('/register/identity-legacy')
def register_identity_legacy():
    """Legacy identity registration page with CIPP covenant selection."""
    # Get biblical nations for selection
    biblical_nations = db.query("""
        SELECT nation_code, nation_name, tribe_name, description, flag_symbol
        FROM biblical_nations
        ORDER BY nation_name
    """)

    return render_template('auth/register_identity.html', biblical_nations=biblical_nations)

@auth_bp.route('/register/genesis')
def register_genesis():
    """Genesis Identity (Platform Founder) registration page."""
    # Check if Genesis Identity already exists
    existing_genesis = db.query_one("SELECT identity_id FROM identities WHERE metadata LIKE '%Platform Founder%'")
    if existing_genesis:
        flash('Genesis Identity already exists. Only one Platform Founder can be created.', 'warning')
        return redirect(url_for('auth.login'))

    return render_template('auth/register_genesis.html')

@auth_bp.route('/register/identity', methods=['POST'])
def register_identity_post():
    """Handle identity registration with CIPP covenant features."""
    try:
        # Get form data
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()
        role = request.form.get('role', 'Citizen')
        nation_of_origin = request.form.get('nation_of_origin', 'JU')
        covenant_accepted = request.form.get('covenant_accepted') == 'on'

        # Validation
        if not name or not email:
            flash('Name and email are required.', 'error')
            biblical_nations = db.query("SELECT * FROM biblical_nations ORDER BY nation_name")
            return render_template('auth/register_identity.html', biblical_nations=biblical_nations)

        if not covenant_accepted:
            flash('You must accept the Digital Scroll of Rights to proceed.', 'error')
            biblical_nations = db.query("SELECT * FROM biblical_nations ORDER BY nation_name")
            return render_template('auth/register_identity.html', biblical_nations=biblical_nations)

        # Check if email already exists
        existing = db.query_one("SELECT identity_id FROM identities WHERE email = ?", (email,))
        if existing:
            flash('An identity with this email already exists.', 'error')
            biblical_nations = db.query("SELECT * FROM biblical_nations ORDER BY nation_name")
            return render_template('auth/register_identity.html', biblical_nations=biblical_nations)

        # Get nation details
        nation_info = db.query_one("""
            SELECT nation_name, tribe_name FROM biblical_nations
            WHERE nation_code = ?
        """, (nation_of_origin,))

        # Generate cryptographic keys
        wallet = Wallet()
        private_key, public_key = wallet.generate_keypair()

        # Create identity with CIPP fields
        current_time = int(time.time())
        identity_data = {
            'name': name,
            'email': email,
            'public_key': public_key,
            'nation_of_origin': nation_of_origin,
            'nation_code': nation_of_origin,
            'nation_name': nation_info['nation_name'] if nation_info else 'Unknown',
            'role_class': role,
            'etzem_score': 10,  # Starting score
            'zeman_count': 0,
            'protection_tier': 'Basic',
            'verification_level': 0,  # Tier 0 - Basic Identity
            'covenant_accepted': covenant_accepted,
            'vault_status': 'Active',
            'last_activity_season': current_time,
            'metadata': json.dumps({
                "purpose": role,
                "registration_type": "web_cipp",
                "verified": True,
                "registration_timestamp": current_time,
                "covenant_version": "v1.0",
                "tribe_name": nation_info['tribe_name'] if nation_info else 'Unknown'
            })
        }

        # Create identity using enhanced data
        identity = Identity.create(**identity_data)

        # Initialize CIPP tracking records
        try:
            # Create verification progress record (Tier 0 completed)
            db.execute("""
                INSERT INTO verification_progress
                (identity_id, tier_0_completed, last_updated)
                VALUES (?, ?, ?)
            """, (identity.identity_id, True, current_time))

            # Record covenant acceptance
            db.execute("""
                INSERT INTO covenant_acceptances
                (identity_id, scroll_version, accepted_at, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            """, (identity.identity_id, 'v1.0', current_time,
                  request.environ.get('REMOTE_ADDR', 'unknown'),
                  request.environ.get('HTTP_USER_AGENT', 'unknown')))

            # Initialize Etzem score history
            db.execute("""
                INSERT INTO etzem_history
                (identity_id, old_score, new_score, change_reason, change_amount, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (identity.identity_id, 0, 10, 'Initial registration bonus', 10, current_time))

            # Create initial Zeman activity period
            db.execute("""
                INSERT INTO zeman_activities
                (identity_id, season_start, season_end, activity_count)
                VALUES (?, ?, ?, ?)
            """, (identity.identity_id, current_time, current_time + (90 * 24 * 3600), 1))

        except Exception as tracking_error:
            logger.warning(f"CIPP tracking initialization failed: {tracking_error}")

        # Store in session
        session['identity_id'] = identity.identity_id
        session['identity_name'] = identity.name
        session['nation_of_origin'] = nation_of_origin
        session['covenant_accepted'] = True

        # Prepare key data for download
        key_data = {
            "identity_id": identity.identity_id,
            "name": identity.name,
            "email": identity.email,
            "nation_of_origin": nation_of_origin,
            "nation_name": nation_info['nation_name'] if nation_info else 'Unknown',
            "role_class": role,
            "public_key": public_key,
            "private_key": private_key,
            "covenant_accepted": covenant_accepted,
            "created_at": current_time,
            "warning": "KEEP THIS FILE SECURE! Your private key controls your covenant identity and assets."
        }

        logger.info(f"CIPP Identity registered successfully: {identity.identity_id} from {nation_of_origin}")

        return render_template('auth/registration_success.html',
                             identity=identity,
                             nation_info=nation_info,
                             key_data=json.dumps(key_data, indent=2))

    except Exception as e:
        logger.error(f"Error registering CIPP identity: {e}")
        flash('Registration failed. Please try again.', 'error')
        biblical_nations = db.query("SELECT * FROM biblical_nations ORDER BY nation_name")
        return render_template('auth/register_identity.html', biblical_nations=biblical_nations)

@auth_bp.route('/register/genesis', methods=['POST'])
def register_genesis_post():
    """Handle Genesis Identity (Platform Founder) registration."""
    try:
        # Check if Genesis Identity already exists
        existing_genesis = db.query_one("SELECT identity_id FROM identities WHERE metadata LIKE '%Platform Founder%'")
        if existing_genesis:
            flash('Genesis Identity already exists. Only one Platform Founder can be created.', 'error')
            return render_template('auth/register_genesis.html')

        # Get form data
        name = request.form.get('name', '').strip()
        email = request.form.get('email', '').strip()
        organization = request.form.get('organization', '').strip()
        purpose = request.form.get('purpose', '').strip()
        role = 'Platform Founder'

        # Validation
        if not name or not email or not purpose:
            flash('Name, email, and purpose statement are required.', 'error')
            return render_template('auth/register_genesis.html')

        # Check if email already exists
        existing = db.query_one("SELECT identity_id FROM identities WHERE email = ?", (email,))
        if existing:
            flash('An identity with this email already exists.', 'error')
            return render_template('auth/register_genesis.html')

        # Generate cryptographic keys
        wallet = Wallet()
        private_key, public_key = wallet.generate_keypair()

        # Create Genesis Identity with special metadata
        genesis_metadata = {
            "role": role,
            "purpose": purpose,
            "organization": organization,
            "genesis_identity": True,
            "platform_founder": True,
            "registration_type": "genesis",
            "verified": True,
            "admin_privileges": True,
            "registration_timestamp": int(time.time()),
            "genesis_block_creator": True
        }

        # Create Genesis Identity
        identity = Identity.create(
            name=name,
            email=email,
            public_key=public_key,
            metadata=genesis_metadata
        )

        # Initialize Genesis Block (this would create Block #0)
        genesis_block_data = {
            "genesis_identity_id": identity.identity_id,
            "founder_name": name,
            "founder_email": email,
            "platform_purpose": purpose,
            "organization": organization,
            "created_at": int(time.time()),
            "block_number": 0,
            "genesis_signature": "GENESIS_BLOCK_SIGNATURE"
        }

        # Store Genesis Block information (simplified for now)
        try:
            db.execute("""
                INSERT OR REPLACE INTO blocks (block_hash, block_number, previous_hash, data, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                "GENESIS_BLOCK_HASH",
                0,
                "0000000000000000000000000000000000000000000000000000000000000000",
                json.dumps(genesis_block_data),
                int(time.time())
            ))
        except Exception as block_error:
            logger.warning(f"Could not create Genesis Block record: {block_error}")

        # Store in session
        session['identity_id'] = identity.identity_id
        session['identity_name'] = identity.name
        session['is_platform_founder'] = True

        # Prepare key data for download
        key_data = {
            "identity_id": identity.identity_id,
            "name": identity.name,
            "email": identity.email,
            "role": role,
            "public_key": public_key,
            "private_key": private_key,
            "genesis_identity": True,
            "platform_founder": True,
            "created_at": int(time.time()),
            "warning": "CRITICAL: This is your Genesis Identity private key. Store it securely in multiple locations!"
        }

        logger.info(f"🌟 Genesis Identity created successfully: {identity.identity_id}")
        flash(f'🌟 Genesis Identity created successfully! Welcome, Platform Founder {identity.name}!', 'success')

        return render_template('auth/genesis_success.html',
                             identity=identity,
                             key_data=json.dumps(key_data, indent=2),
                             genesis_block_data=genesis_block_data)

    except Exception as e:
        logger.error(f"Error creating Genesis Identity: {e}")
        flash('Genesis Identity creation failed. Please try again.', 'error')
        return render_template('auth/register_genesis.html')

@auth_bp.route('/register/sela')
def register_sela():
    """Sela registration page - different flows for new vs existing users."""
    if 'identity_id' not in session:
        flash('Please complete the Eden Mode identity creation first.', 'warning')
        return redirect(url_for('eden_mode.step1'))

    # Check if this is coming from dashboard (existing user flow)
    from_dashboard = request.args.get('from_dashboard', False)
    if from_dashboard:
        # For dashboard users, return to dashboard with popup trigger
        return redirect(url_for('dashboard.overview', show_sela_popup='true'))

    # For other flows, redirect to Eden Mode step 4
    return redirect(url_for('eden_mode.step4'))

@auth_bp.route('/register/sela-legacy')
def register_sela_legacy():
    """Legacy Sela registration page."""
    if 'identity_id' not in session:
        flash('Please register an identity first.', 'warning')
        return redirect(url_for('auth.register_identity'))

    # Get business categories
    categories = [
        'Technology', 'Consulting', 'Retail', 'Manufacturing',
        'Healthcare', 'Education', 'Finance', 'Real Estate',
        'Food & Beverage', 'Transportation', 'Entertainment', 'Other'
    ]

    return render_template('auth/register_sela.html', categories=categories)

@auth_bp.route('/register/sela', methods=['POST'])
def register_sela_post():
    """Handle Sela registration."""
    try:
        if 'identity_id' not in session:
            flash('Please log in first.', 'error')
            return redirect(url_for('auth.register_identity'))

        # Get form data
        sela_name = request.form.get('sela_name', '').strip()
        category = request.form.get('category', '').strip()
        description = request.form.get('description', '').strip()
        address = request.form.get('address', '').strip()
        services = request.form.getlist('services')

        # Validation
        if not sela_name or not category or not description:
            flash('Business name, category, and description are required.', 'error')
            return redirect(url_for('auth.register_sela'))

        # Check if Sela name already exists
        existing = db.query_one("SELECT sela_id FROM selas WHERE name = ?", (sela_name,))
        if existing:
            flash('A business with this name already exists.', 'error')
            return redirect(url_for('auth.register_sela'))

        # Generate Sela ID
        import hashlib
        sela_id = hashlib.sha256(f"{sela_name}_{session['identity_id']}_{int(time.time())}".encode()).hexdigest()[:16]

        # Create Sela data
        sela_data = {
            'sela_id': sela_id,
            'identity_id': session['identity_id'],
            'name': sela_name,
            'category': category,
            'stake_amount': 1000,  # Default stake amount
            'stake_token_id': 'ONX',  # Default to ONX token
            'status': 'active',
            'created_at': int(time.time()),
            'metadata': json.dumps({
                "description": description,
                "address": address,
                "services": services,
                "registration_type": "web",
                "verified": True
            })
        }

        # Insert into database
        db.insert('selas', sela_data)

        logger.info(f"Sela registered successfully: {sela_id}")
        flash(f'Business "{sela_name}" registered successfully!', 'success')

        return redirect(url_for('dashboard.overview'))

    except Exception as e:
        logger.error(f"Error registering Sela: {e}")
        flash('Sela registration failed. Please try again.', 'error')
        return redirect(url_for('auth.register_sela'))

@auth_bp.route('/login')
def login():
    """Login page."""
    return render_template('auth/login.html')

@auth_bp.route('/login', methods=['POST'])
def login_post():
    """Handle secure login with password verification."""
    try:
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '')

        if not email or not password:
            flash('Email and password are required.', 'error')
            return render_template('auth/login.html')

        # Check login attempts rate limiting
        if not SecureAuth.check_login_attempts(email):
            SecureAuth.log_auth_event('LOGIN_BLOCKED_RATE_LIMIT', None, {'email': email})
            flash('Too many failed login attempts. Please try again in 15 minutes.', 'error')
            return render_template('auth/login.html')

        # Find identity by email
        identity = db.query_one("SELECT * FROM identities WHERE email = ?", (email,))

        if not identity:
            SecureAuth.record_login_attempt(email, False)
            SecureAuth.log_auth_event('LOGIN_FAILED_NO_USER', None, {'email': email})
            flash('Invalid email or password.', 'error')
            return render_template('auth/login.html')

        identity_id = identity['identity_id']

        # Check if password is set for this identity
        password_record = db.query_one("""
            SELECT password_hash, salt FROM identity_passwords
            WHERE identity_id = ?
        """, (identity_id,))

        if not password_record:
            # For backward compatibility, allow Genesis Identity without password
            metadata = {}
            if identity.get('metadata'):
                try:
                    metadata = json.loads(identity['metadata'])
                except:
                    pass

            if metadata.get('genesis_identity') or metadata.get('admin_privileges'):
                # Genesis Identity - create secure session without password for now
                logger.warning(f"SECURITY: Genesis Identity {identity_id} logged in without password")
                user_data = {
                    'name': identity['name'],
                    'email': identity['email'],
                    'role': 'system_admin',
                    'is_admin': True,
                    'is_genesis': True
                }
                SecureAuth.create_secure_session(identity_id, user_data)
                SecureAuth.record_login_attempt(email, True, identity_id)
                flash(f'Welcome back, {identity["name"]}! Please set a password for security.', 'warning')
                return redirect(url_for('dashboard.overview'))
            else:
                SecureAuth.record_login_attempt(email, False, identity_id)
                flash('Password not set for this account. Please contact administrator.', 'error')
                return render_template('auth/login.html')

        # Verify password
        if not SecureAuth.verify_password(password, password_record['password_hash'], password_record['salt']):
            SecureAuth.record_login_attempt(email, False, identity_id)
            SecureAuth.log_auth_event('LOGIN_FAILED_WRONG_PASSWORD', identity_id, {'email': email})
            flash('Invalid email or password.', 'error')
            return render_template('auth/login.html')

        # Successful login - create secure session
        user_data = {
            'name': identity['name'],
            'email': identity['email'],
            'nation_code': identity.get('nation_code'),
            'tribe_name': identity.get('tribe_name'),
            'verification_level': identity.get('verification_level', 0)
        }

        SecureAuth.create_secure_session(identity_id, user_data)
        SecureAuth.record_login_attempt(email, True, identity_id)

        flash(f'Welcome back, {identity["name"]}!', 'success')
        return redirect(url_for('dashboard.overview'))

    except Exception as e:
        logger.error(f"Error during login: {e}")
        SecureAuth.log_auth_event('LOGIN_ERROR', None, {'error': str(e), 'email': email})
        flash('Login failed. Please try again.', 'error')
        return render_template('auth/login.html')

@auth_bp.route('/logout')
def logout():
    """Secure logout with session destruction."""
    try:
        identity_id = session.get('identity_id')
        if identity_id:
            SecureAuth.log_auth_event('LOGOUT', identity_id)

        SecureAuth.destroy_session()
        flash('You have been logged out.', 'info')
        return redirect(url_for('index'))

    except Exception as e:
        logger.error(f"Error during logout: {e}")
        session.clear()  # Fallback
        return redirect(url_for('index'))

@auth_bp.route('/api/validate/email', methods=['POST'])
def validate_email():
    """API endpoint to validate email availability."""
    try:
        data = request.get_json()
        email = data.get('email', '').strip().lower()

        if not email:
            return jsonify({'valid': False, 'message': 'Email is required'})

        # Basic email format validation
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            return jsonify({'valid': False, 'message': 'Invalid email format'})

        # Check if email already exists
        existing = db.query_one("SELECT identity_id FROM identities WHERE email = ?", (email,))
        if existing:
            return jsonify({'valid': False, 'message': 'Email already registered'})

        return jsonify({'valid': True, 'message': 'Email available'})

    except Exception as e:
        logger.error(f"Email validation error: {e}")
        return jsonify({'valid': False, 'message': 'Validation failed'})

@auth_bp.route('/api/validate/business', methods=['POST'])
def validate_business():
    """API endpoint to validate business name availability."""
    try:
        data = request.get_json()
        business_name = data.get('business_name', '').strip()

        if not business_name:
            return jsonify({'valid': False, 'message': 'Business name is required'})

        # Check if business name already exists
        existing = db.query_one("SELECT sela_id FROM selas WHERE name = ?", (business_name,))
        if existing:
            return jsonify({'valid': False, 'message': 'Business name already registered'})

        return jsonify({'valid': True, 'message': 'Business name available'})

    except Exception as e:
        logger.error(f"Business validation error: {e}")
        return jsonify({'valid': False, 'message': 'Validation failed'})

# CIPP (Covenant Identity Protection Protocol) Routes

@auth_bp.route('/covenant-scroll')
def covenant_scroll():
    """Display the Digital Scroll of Rights."""
    return render_template('auth/covenant_scroll.html')

@auth_bp.route('/api/identity/<identity_id>/protection-status')
def get_protection_status(identity_id):
    """Get current protection tier and vault status."""
    try:
        identity = db.query_one("""
            SELECT protection_tier, vault_status, verification_level, etzem_score
            FROM identities WHERE identity_id = ?
        """, (identity_id,))

        if not identity:
            return jsonify({'error': 'Identity not found'}), 404

        # Get recent protection events
        recent_events = db.query("""
            SELECT event_type, timestamp, reason
            FROM vault_events
            WHERE identity_id = ?
            ORDER BY timestamp DESC
            LIMIT 5
        """, (identity_id,))

        return jsonify({
            'protection_tier': identity['protection_tier'],
            'vault_status': identity['vault_status'],
            'verification_level': identity['verification_level'],
            'etzem_score': identity['etzem_score'],
            'recent_events': recent_events
        })

    except Exception as e:
        logger.error(f"Error getting protection status: {e}")
        return jsonify({'error': 'Failed to get protection status'}), 500

@auth_bp.route('/api/identity/<identity_id>/request-protection', methods=['POST'])
def request_protection(identity_id):
    """Request emergency protection activation."""
    try:
        data = request.get_json()
        request_type = data.get('type', 'EMERGENCY_FREEZE')
        reason = data.get('reason', 'User requested protection')

        # Validate request type
        valid_types = ['EMERGENCY_FREEZE', 'VAULT_LOCK', 'EXILE_MODE']
        if request_type not in valid_types:
            return jsonify({'error': 'Invalid protection type'}), 400

        # Create protection request
        request_id = f"PR_{int(time.time())}_{identity_id[:8]}"

        db.execute("""
            INSERT INTO protection_requests
            (request_id, identity_id, request_type, reason, requested_at)
            VALUES (?, ?, ?, ?, ?)
        """, (request_id, identity_id, request_type, reason, int(time.time())))

        # Log vault event
        db.execute("""
            INSERT INTO vault_events
            (identity_id, event_type, triggered_by, reason, timestamp)
            VALUES (?, ?, ?, ?, ?)
        """, (identity_id, f"{request_type}_REQUESTED", 'USER', reason, int(time.time())))

        return jsonify({
            'success': True,
            'request_id': request_id,
            'message': f'Protection request {request_type} submitted successfully'
        })

    except Exception as e:
        logger.error(f"Error requesting protection: {e}")
        return jsonify({'error': 'Failed to request protection'}), 500

@auth_bp.route('/api/identity/<identity_id>/verification-progress')
def get_verification_progress(identity_id):
    """Get current verification tier and upgrade requirements."""
    try:
        # Get verification progress
        progress = db.query_one("""
            SELECT * FROM verification_progress WHERE identity_id = ?
        """, (identity_id,))

        if not progress:
            return jsonify({'error': 'Verification progress not found'}), 404

        # Get identity details
        identity = db.query_one("""
            SELECT verification_level, etzem_score, role_class
            FROM identities WHERE identity_id = ?
        """, (identity_id,))

        # Calculate next tier requirements
        next_tier_requirements = get_next_tier_requirements(identity['verification_level'])

        return jsonify({
            'current_tier': identity['verification_level'],
            'tier_progress': {
                'tier_0': progress['tier_0_completed'],
                'tier_1': progress['tier_1_completed'],
                'tier_2': progress['tier_2_completed'],
                'tier_3': progress['tier_3_completed']
            },
            'tier_1_method': progress['tier_1_method'],
            'tier_2_contributions': progress['tier_2_contributions'],
            'tier_3_governance_count': progress['tier_3_governance_count'],
            'etzem_score': identity['etzem_score'],
            'role_class': identity['role_class'],
            'next_tier_requirements': next_tier_requirements
        })

    except Exception as e:
        logger.error(f"Error getting verification progress: {e}")
        return jsonify({'error': 'Failed to get verification progress'}), 500

@auth_bp.route('/api/identity/<identity_id>/accept-covenant', methods=['POST'])
def accept_covenant(identity_id):
    """Record Digital Scroll acceptance."""
    try:
        data = request.get_json()
        scroll_version = data.get('version', 'v1.0')
        signature_hash = data.get('signature_hash', '')

        # Check if already accepted
        existing = db.query_one("""
            SELECT identity_id FROM covenant_acceptances WHERE identity_id = ?
        """, (identity_id,))

        if existing:
            return jsonify({'error': 'Covenant already accepted'}), 400

        # Record acceptance
        db.execute("""
            INSERT INTO covenant_acceptances
            (identity_id, scroll_version, accepted_at, ip_address, user_agent, signature_hash)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (identity_id, scroll_version, int(time.time()),
              request.environ.get('REMOTE_ADDR', 'unknown'),
              request.environ.get('HTTP_USER_AGENT', 'unknown'),
              signature_hash))

        # Update identity covenant status
        db.execute("""
            UPDATE identities SET covenant_accepted = 1 WHERE identity_id = ?
        """, (identity_id,))

        return jsonify({
            'success': True,
            'message': 'Covenant accepted successfully',
            'scroll_version': scroll_version
        })

    except Exception as e:
        logger.error(f"Error accepting covenant: {e}")
        return jsonify({'error': 'Failed to accept covenant'}), 500

@auth_bp.route('/api/nations/list')
def get_nations_list():
    """Get available biblical nations for selection."""
    try:
        nations = db.query("""
            SELECT nation_code, nation_name, tribe_name, description, flag_symbol
            FROM biblical_nations
            ORDER BY nation_name
        """)

        return jsonify({
            'nations': nations,
            'total_count': len(nations)
        })

    except Exception as e:
        logger.error(f"Error getting nations list: {e}")
        return jsonify({'error': 'Failed to get nations list'}), 500

def get_next_tier_requirements(current_tier):
    """Get requirements for the next verification tier."""
    requirements = {
        0: {
            'tier': 1,
            'name': 'Proof of Humanity',
            'requirements': [
                '2FA verification OR biometric proof OR trusted validator endorsement',
                'Complete identity verification process'
            ]
        },
        1: {
            'tier': 2,
            'name': 'Proof of Labor',
            'requirements': [
                'Minimum 3 logged contributions',
                'Service transactions, governance participation, or community tasks',
                'Integration with Sela validator system'
            ]
        },
        2: {
            'tier': 3,
            'name': 'Covenant Holder',
            'requirements': [
                'Active Sela operation OR governance participation',
                'Submitted scrolls or voting records',
                'Biblical tokenomics system participation'
            ]
        },
        3: {
            'tier': 3,
            'name': 'Maximum Tier Achieved',
            'requirements': [
                'You have achieved the highest verification tier',
                'Continue participating in governance and community activities'
            ]
        }
    }

    return requirements.get(current_tier, requirements[3])

@auth_bp.route('/identity/dashboard')
def identity_dashboard():
    """Identity dashboard with CIPP covenant features."""
    if 'identity_id' not in session:
        flash('Please log in to access your identity dashboard.', 'warning')
        return redirect(url_for('auth.login'))

    try:
        identity_id = session['identity_id']

        # Get enhanced identity information
        identity = db.query_one("""
            SELECT * FROM identities WHERE identity_id = ?
        """, (identity_id,))

        if not identity:
            flash('Identity not found.', 'error')
            return redirect(url_for('auth.login'))

        # Get nation information
        nation_info = db.query_one("""
            SELECT * FROM biblical_nations WHERE nation_code = ?
        """, (identity.get('nation_of_origin', 'JU'),))

        # Get verification progress
        verification_progress = db.query_one("""
            SELECT * FROM verification_progress WHERE identity_id = ?
        """, (identity_id,))

        if not verification_progress:
            # Create default verification progress
            db.execute("""
                INSERT INTO verification_progress
                (identity_id, tier_0_completed, last_updated)
                VALUES (?, ?, ?)
            """, (identity_id, True, int(time.time())))
            verification_progress = {
                'tier_0_completed': True,
                'tier_1_completed': False,
                'tier_2_completed': False,
                'tier_3_completed': False,
                'tier_1_method': None,
                'tier_2_contributions': 0,
                'tier_3_governance_count': 0
            }

        # Get Etzem score history
        etzem_history = db.query("""
            SELECT * FROM etzem_history
            WHERE identity_id = ?
            ORDER BY timestamp DESC
            LIMIT 10
        """, (identity_id,))

        # Get Zeman activities
        zeman_activities = db.query("""
            SELECT * FROM zeman_activities
            WHERE identity_id = ?
            ORDER BY season_start DESC
            LIMIT 5
        """, (identity_id,))

        # Get recent contributions (placeholder)
        recent_contributions = [
            {
                'type': 'Community Service',
                'description': 'Helped onboard new covenant members',
                'icon': '🤝',
                'etzem_value': 5,
                'timestamp': int(time.time()) - (2 * 24 * 3600)
            },
            {
                'type': 'Governance Participation',
                'description': 'Voted on network upgrade proposal',
                'icon': '🗳️',
                'etzem_value': 3,
                'timestamp': int(time.time()) - (5 * 24 * 3600)
            },
            {
                'type': 'Economic Activity',
                'description': 'Completed Sela service transaction',
                'icon': '💼',
                'etzem_value': 8,
                'timestamp': int(time.time()) - (7 * 24 * 3600)
            }
        ]

        # Calculate tier names and next requirements
        tier_names = {
            0: 'Basic Identity',
            1: 'Proof of Humanity',
            2: 'Proof of Labor',
            3: 'Covenant Holder'
        }

        next_tier_requirements = get_next_tier_requirements(identity.get('verification_level', 0))

        # Calculate Etzem level
        etzem_score = identity.get('etzem_score', 0)
        if etzem_score >= 80:
            etzem_level = 'Righteous Elder'
        elif etzem_score >= 60:
            etzem_level = 'Faithful Servant'
        elif etzem_score >= 40:
            etzem_level = 'Growing Disciple'
        elif etzem_score >= 20:
            etzem_level = 'New Covenant Member'
        else:
            etzem_level = 'Seeking Wisdom'

        return render_template('identity/dashboard.html',
                             identity=identity,
                             nation_info=nation_info or {'flag_symbol': '🛡️', 'tribe_name': 'Unknown'},
                             verification_progress=verification_progress,
                             etzem_history=etzem_history,
                             zeman_activities=zeman_activities,
                             recent_contributions=recent_contributions,
                             tier_names=tier_names,
                             next_tier_requirements=next_tier_requirements,
                             etzem_level=etzem_level,
                             contributions_count=len(recent_contributions))

    except Exception as e:
        logger.error(f"Error loading identity dashboard: {e}")
        flash('Error loading dashboard. Please try again.', 'error')
        return redirect(url_for('dashboard.overview'))
