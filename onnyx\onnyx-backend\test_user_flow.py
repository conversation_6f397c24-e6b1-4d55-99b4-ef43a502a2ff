#!/usr/bin/env python3
"""
End-to-End User Flow Testing for ONNYX Platform
Tests complete user registration, Sela creation, and mining functionality
"""

import requests
import json
import time
import sys

class UserFlowTester:
    def __init__(self, base_url="http://127.0.0.1:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_genesis_login(self):
        """Test Genesis Identity login"""
        print("👑 TESTING GENESIS IDENTITY LOGIN")
        print("-" * 40)
        
        try:
            # Get login page first to get CSRF token
            login_page = self.session.get(f"{self.base_url}/auth/login")
            print(f"  Login page status: {login_page.status_code}")
            
            # Try to login with Genesis credentials
            login_data = {
                'email': '<EMAIL>',
                'password': 'Genesis2024!Secure'
            }
            
            response = self.session.post(f"{self.base_url}/auth/login", data=login_data)
            print(f"  Login attempt status: {response.status_code}")
            
            if response.status_code == 302:  # Redirect after successful login
                print("  ✅ Genesis login successful")
                return True
            else:
                print(f"  ❌ Genesis login failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ Genesis login error: {e}")
            return False
    
    def test_admin_access(self):
        """Test admin dashboard access after login"""
        print("\n🔐 TESTING ADMIN DASHBOARD ACCESS")
        print("-" * 40)
        
        try:
            response = self.session.get(f"{self.base_url}/onboarding/")
            print(f"  Admin dashboard status: {response.status_code}")
            
            if response.status_code == 200:
                print("  ✅ Admin dashboard accessible")
                return True
            else:
                print(f"  ❌ Admin dashboard access denied: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ Admin dashboard error: {e}")
            return False
    
    def test_eden_mode_registration(self):
        """Test Eden Mode user registration"""
        print("\n🌟 TESTING EDEN MODE REGISTRATION")
        print("-" * 40)
        
        try:
            # Get Eden Mode step 1
            step1_response = self.session.get(f"{self.base_url}/auth/eden-mode/step1")
            print(f"  Eden Mode step 1 status: {step1_response.status_code}")
            
            if step1_response.status_code != 200:
                print("  ❌ Cannot access Eden Mode")
                return False
            
            # Test access validation
            validation_data = {
                'password': 'secure_covenant_2024',
                'path_type': 'israelite'
            }
            
            validation_response = self.session.post(
                f"{self.base_url}/auth/eden-mode/validate-access",
                json=validation_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"  Access validation status: {validation_response.status_code}")
            
            if validation_response.status_code == 200:
                print("  ✅ Eden Mode access validation working")
                
                # Test step 2 access
                step2_response = self.session.get(f"{self.base_url}/auth/eden-mode/israelite-step2")
                print(f"  Eden Mode step 2 status: {step2_response.status_code}")
                
                if step2_response.status_code == 200:
                    print("  ✅ Eden Mode step 2 accessible")
                    return True
                else:
                    print(f"  ❌ Eden Mode step 2 access failed: {step2_response.status_code}")
                    return False
            else:
                print(f"  ❌ Eden Mode access validation failed: {validation_response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ Eden Mode registration error: {e}")
            return False
    
    def test_user_registration(self):
        """Test complete user registration through Eden Mode"""
        print("\n👤 TESTING USER REGISTRATION")
        print("-" * 40)
        
        try:
            # Create test user data
            user_data = {
                'fullName': 'Test User',
                'email': '<EMAIL>',
                'selectedNation': 'JU',
                'selectedTribe': 'Judah',
                'laborCategory': 'Technology',
                'selaChoice': 'create_new',
                'selaName': 'TestSela Corp',
                'selaDescription': 'A test business entity for ONNYX platform testing',
                'selaCategory': 'Technology'
            }
            
            # Submit registration
            response = self.session.post(
                f"{self.base_url}/auth/eden-mode/create-identity",
                json=user_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"  User registration status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("  ✅ User registration successful")
                    print(f"  Identity ID: {result.get('identity_id', 'N/A')}")
                    return True
                else:
                    print(f"  ❌ User registration failed: {result.get('error', 'Unknown error')}")
                    return False
            else:
                print(f"  ❌ User registration failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ User registration error: {e}")
            return False
    
    def test_sela_creation(self):
        """Test Sela (business entity) creation"""
        print("\n🏢 TESTING SELA CREATION")
        print("-" * 40)
        
        try:
            # Test Sela creation API
            sela_data = {
                'name': 'Test Mining Corp',
                'description': 'A test mining business entity',
                'category': 'Mining'
            }
            
            response = self.session.post(
                f"{self.base_url}/api/sela/create-dashboard",
                json=sela_data,
                headers={'Content-Type': 'application/json'}
            )
            
            print(f"  Sela creation status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("  ✅ Sela creation successful")
                    return True
                else:
                    print(f"  ❌ Sela creation failed: {result.get('error', 'Unknown error')}")
                    return False
            else:
                print(f"  ❌ Sela creation failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ Sela creation error: {e}")
            return False
    
    def test_mining_functionality(self):
        """Test mining functionality"""
        print("\n⛏️ TESTING MINING FUNCTIONALITY")
        print("-" * 40)
        
        try:
            # Test mining status API
            response = self.session.get(f"{self.base_url}/api/mining/status")
            print(f"  Mining status API: {response.status_code}")
            
            if response.status_code == 200:
                print("  ✅ Mining status API accessible")
                return True
            else:
                print(f"  ❌ Mining status API failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ Mining functionality error: {e}")
            return False
    
    def run_complete_test(self):
        """Run complete end-to-end test"""
        print("🚀 ONNYX END-TO-END USER FLOW TESTING")
        print("=" * 60)
        
        tests = [
            ("Genesis Login", self.test_genesis_login),
            ("Admin Access", self.test_admin_access),
            ("Eden Mode Registration", self.test_eden_mode_registration),
            ("User Registration", self.test_user_registration),
            ("Sela Creation", self.test_sela_creation),
            ("Mining Functionality", self.test_mining_functionality)
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"  ❌ {test_name} crashed: {e}")
                results.append((test_name, False))
        
        # Generate report
        print("\n" + "=" * 60)
        print("📊 END-TO-END TEST REPORT")
        print("=" * 60)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"\n📈 SUMMARY:")
        print(f"  • Total Tests: {total}")
        print(f"  • ✅ Passed: {passed}")
        print(f"  • ❌ Failed: {total - passed}")
        print(f"  • Success Rate: {(passed/total)*100:.1f}%")
        
        print(f"\n📋 TEST RESULTS:")
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status}: {test_name}")
        
        if passed == total:
            print(f"\n🎉 ALL TESTS PASSED - ONNYX PLATFORM FULLY FUNCTIONAL!")
            return True
        else:
            print(f"\n⚠️ SOME TESTS FAILED - PLATFORM NEEDS ATTENTION")
            return False

def main():
    """Run end-to-end testing"""
    tester = UserFlowTester()
    success = tester.run_complete_test()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
